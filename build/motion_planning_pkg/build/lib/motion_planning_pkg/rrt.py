"""Utilities for manipulation-specific Rapidly-Exploring Random Trees (RRTs)."""

import numpy as np
import time

from .utils import (
    check_collisions_at_state,
    check_collisions_along_path,
    configuration_distance,
    extract_cartesian_poses,
    get_random_state,
)

from .graph import Node, Graph
from .utils import (
    discretize_joint_space_path,
    extend_robot_state,
    has_collision_free_path,
    retrace_path,
)


class RRTPlannerOptions:
    """Options for Rapidly-exploring Random Tree (RRT) planning."""

    def __init__(
        self,
        max_step_size=0.05,
        max_connection_dist=np.inf,
        rrt_connect=False,
        bidirectional_rrt=False,
        rrt_star=False,
        max_rewire_dist=np.inf,
        max_planning_time=10.0,
        rng_seed=None,
        fast_return=True,
        goal_biasing_probability=0.0,
        collision_distance_padding=0.0,
        verbose=False,
    ):
        """
        Initializes a set of RRT planner options.

        Parameters
        ----------
            max_step_size : float
                Maximum joint configuration step size for collision checking along path segments.
            max_connection_dist : float
                Maximum angular distance, in radians, for connecting nodes.
            rrt_connect : bool
                If true, enables the RRTConnect algorithm, which incrementally extends the most
                recently sampled node in the tree until an invalid state is reached.
            bidirectional_rrt : bool
                If true, uses bidirectional RRTs from both start and goal nodes.
                Otherwise, only grows a tree from the start node.
            rrt_star : bool
                If true, enables the RRT* algorithm to shortcut node connections during planning.
                This in turn will use the `max_rewire_dist` parameter.
            max_rewire_dist : float
                Maximum angular distance, in radians, to consider rewiring nodes for RRT*.
                If set to `np.inf`, all nodes in the trees will be considered for rewiring.
            max_planning_time : float
                Maximum planning time, in seconds.
            rng_seed : int, optional
                Sets the seed for random number generation. Use to generate deterministic results.
            fast_return : bool
                If True, return as soon as a solution is found. Otherwise continuing building the tree
                until max_planning_time is reached.
            goal_biasing_probability : float
                Probability of sampling the goal configuration itself, which can help planning converge.
            collision_distance_padding : float
                The padding, in meters, to use for distance to nearest collision.
        """
        self.max_step_size = max_step_size
        self.max_connection_dist = max_connection_dist
        self.rrt_connect = rrt_connect
        self.bidirectional_rrt = bidirectional_rrt
        self.rrt_star = rrt_star
        self.max_rewire_dist = max_rewire_dist
        self.max_planning_time = max_planning_time
        self.rng_seed = rng_seed
        self.fast_return = fast_return
        self.goal_biasing_probability = goal_biasing_probability
        self.collision_distance_padding = collision_distance_padding
        self.verbose = verbose


class RRTPlanner:
    """Rapidly-expanding Random Tree (RRT) planner.

    This is a sampling-based motion planner that finds collision-free paths from a start to a goal configuration.

    Some good resources:
      * Original RRT paper: https://msl.cs.illinois.edu/~lavalle/papers/Lav98c.pdf
      * RRTConnect paper: https://www.cs.cmu.edu/afs/cs/academic/class/15494-s14/readings/kuffner_icra2000.pdf
      * RRT* and PRM* paper: https://arxiv.org/abs/1105.1186
    """

    def __init__(self, model, collision_model, options=RRTPlannerOptions()):
        """
        Creates an instance of an RRT planner.

        Parameters
        ----------
            model : `pinocchio.Model`
                The model to use for this solver.
            collision_model : `pinocchio.Model`
                The model to use for collision checking.
            options : `RRTPlannerOptions`, optional
                The options to use for planning. If not specified, default options are used.
        """
        self.model = model
        self.collision_model = collision_model
        self.data = self.model.createData()
        self.collision_data = self.collision_model.createData()
        self.options = options
        self.reset()

    def reset(self):
        """Resets all the planning data structures."""
        self.latest_path = None
        self.start_tree = Graph()
        self.goal_tree = Graph()
        np.random.seed(self.options.rng_seed)

    def plan(self, q_start, q_goal):
        """
        Plans a path from a start to a goal configuration.

        Parameters
        ----------
            q_start : array-like
                The starting robot configuration.
            q_goal : array-like
                The goal robot configuration.
        """
        verbose = self.options.verbose
        # 重置规划器状态，包括清空树和路径
        self.reset()
        t_start = time.time()  # 记录规划开始时间

        # 初始化起始节点和目标节点，并将它们添加到对应的树中
        start_node = Node(q_start, parent=None, cost=0.0)
        self.start_tree.add_node(start_node)
        goal_node = Node(q_goal, parent=None, cost=0.0)
        self.goal_tree.add_node(goal_node)

        best_path = None  # 记录最优路径
        best_cost = float('inf')  # 记录最优路径的成本
        
        goal_found = False  # 标记是否找到路径
        latest_start_tree_node = start_node  # 起始树的最新节点
        latest_goal_tree_node = goal_node  # 目标树的最新节点

        # 检查起始和目标配置是否发生碰撞
        if check_collisions_at_state(
            self.model,
            self.collision_model,
            q_start,
            self.data,
            self.collision_data,
            distance_padding=self.options.collision_distance_padding,
        ):
            if verbose:
                print("Start configuration in collision.")
            return None
        if check_collisions_at_state(
            self.model,
            self.collision_model,
            q_goal,
            self.data,
            self.collision_data,
            distance_padding=self.options.collision_distance_padding,
        ):
            if verbose:
                print("Goal configuration in collision.")
            return None

        # 检查是否可以直接连接起始和目标配置
        if configuration_distance(q_start, q_goal) <= self.options.max_connection_dist:
            path_to_goal = discretize_joint_space_path(
                [q_start, q_goal], self.options.max_step_size
            )
            if not check_collisions_along_path(
                self.model,
                self.collision_model,
                path_to_goal,
                distance_padding=self.options.collision_distance_padding,
            ):
                # 如果可以直接连接，将目标节点添加到起始树中
                latest_start_tree_node = self.add_node_to_tree(
                    self.start_tree, q_goal, start_node
                )
                if verbose:
                    print("Start and goal can be directly connected!")
                goal_found = True
                best_path = self.extract_path_from_trees(latest_start_tree_node, None)
                best_cost = sum(np.linalg.norm(best_path[i]-best_path[i-1]) 
                              for i in range(1, len(best_path)))

        start_tree_phase = True  # 标记当前是否扩展起始树
        while True:
            # 检查是否超时
            if time.time() - t_start > self.options.max_planning_time:
                if best_path is not None:
                    if verbose:
                        print(f"Planning completed with best path cost: {best_cost:.3f}")
                    self.latest_path = best_path
                    return self.latest_path
                else:
                    if verbose:
                        print("Planning timed out without finding a valid path.")
                    return None

            # 如果找到路径且启用了快速返回选项，则退出循环
            if goal_found and self.options.fast_return:
                break

            # 根据当前阶段选择扩展的树和另一棵树
            current_tree = self.start_tree if start_tree_phase else self.goal_tree
            other_tree = self.goal_tree if start_tree_phase else self.start_tree

            # 采样新配置
            if np.random.random() < self.options.goal_biasing_probability:
                # 根据目标偏置概率，选择目标配置或随机采样
                q_sample = q_goal if start_tree_phase else q_start
            else:
                q_sample = get_random_state(self.model)

            # 单次扩展第一棵树(current_tree)
            nearest_node = current_tree.get_nearest_node(q_sample)
            current_tree_new_node = self.extend(current_tree, nearest_node, q_sample)
            if current_tree_new_node is not None and self.options.rrt_star:
                # 如果启用了 RRT*，则进行重连
                self.rewire_tree(current_tree, current_tree_new_node)

            # 如果扩展成功，将新节点添加到树中
            if current_tree_new_node is not None:
                if start_tree_phase:
                    latest_start_tree_node = current_tree_new_node
                else:
                    latest_goal_tree_node = current_tree_new_node
                    
                # 扩展第二棵树(other_tree)
                other_tree_nearest_node = other_tree.get_nearest_node(current_tree_new_node.q)
                
                cur_parent_node = other_tree_nearest_node
                while True:
                    # 第二棵树(other_tree)开始向第一棵树(current_tree)扩展
                    other_tree_new_node = self.extend(other_tree, cur_parent_node, current_tree_new_node.q)
                    
                    # 如果扩展成功，进行重连
                    if other_tree_new_node is not None and self.options.rrt_star:
                        # 如果启用了 RRT*，则进行重连
                        self.rewire_tree(other_tree, other_tree_new_node)
                    else:
                        # 如果扩展失败，则跳过
                        break
                    
                    # 如果新节点与当前节点相同(扩展完成)，则跳过
                    if np.array_equal(other_tree_new_node.q, current_tree_new_node.q):
                        break
                    
                    # 如果未启用 RRT-Connect，则只扩展一次，如果启用 RRT-Connect，则继续贪婪扩展
                    if not self.options.rrt_connect:
                        break
                    
                    # 如果扩展成功，则更新当前父节点
                    cur_parent_node = other_tree_new_node
                    
                # 如果扩展成功，将新节点添加到第二棵树(other_tree)中
                if other_tree_new_node is not None:
                    if start_tree_phase:
                        latest_goal_tree_node = other_tree_new_node
                    else:
                        latest_start_tree_node = other_tree_new_node
                    
                # 检查第二棵树(other_tree)扩展的新节点是否可以直接连接到第一棵树(current_tree)
                if other_tree_new_node is not None:
                    distance_to_other_tree = configuration_distance(
                        other_tree_new_node.q, current_tree_new_node.q)
                
                # print("Distance to other tree:", distance_to_other_tree)
                
                if other_tree_new_node is not None and distance_to_other_tree <= self.options.max_connection_dist:
                    # 碰撞检查
                    path_to_other_tree = discretize_joint_space_path(
                        [current_tree_new_node.q, other_tree_new_node.q],
                        self.options.max_step_size,
                    )
                    if not check_collisions_along_path(
                        self.model,
                        self.collision_model,
                        path_to_other_tree,
                        distance_padding=self.options.collision_distance_padding,
                    ):
                        # 如果可以连接，将新节点添加到第二棵树(other_tree)中
                        if distance_to_other_tree > 0:
                            other_tree_new_node = self.add_node_to_tree(
                                other_tree, current_tree_new_node.q, other_tree_new_node
                            )
                        # 更新两棵树的最新节点
                        if start_tree_phase:
                            latest_start_tree_node = current_tree_new_node
                            latest_goal_tree_node = other_tree_new_node
                        else:
                            latest_start_tree_node = other_tree_new_node
                            latest_goal_tree_node = current_tree_new_node
                        goal_found = True
                        
                        # 计算新路径的cost并更新最优路径
                        current_path = self.extract_path_from_trees(
                            latest_start_tree_node, latest_goal_tree_node
                        )
                        current_cost = sum(np.linalg.norm(current_path[i]-current_path[i-1]) 
                                         for i in range(1, len(current_path)))
                        if current_cost < best_cost:
                            best_path = current_path
                            best_cost = current_cost
                        
                        if verbose:    
                            print(f"Found new path with cost: {current_cost} , better path with cost: {best_cost:.3f}")

                # 如果启用了双向 RRT，切换到另一棵树
                if self.options.bidirectional_rrt:
                    start_tree_phase = not start_tree_phase

        # 如果找到路径，回溯生成完整路径
        self.latest_path = []
        if goal_found:
            self.latest_path = self.extract_path_from_trees(
                latest_start_tree_node, latest_goal_tree_node
            )
        return self.latest_path

    def extend(self, tree, parent_node, q_sample):
        """
        Extends a tree towards a sampled node with a single step

        Parameters
        ----------
            tree : `pyroboplan.planning.graph.Graph`
                The tree to use when performing this operation.
            parent_node : `pyroboplan.planning.graph.Node`
                The node from which to start extending towards the sample.
            q_sample : array-like
                The robot configuration sample to extend towards.

        Return
        ------
            `pyroboplan.planning.graph.Node`, optional
                The new node that was added to the tree, or `None` if no node was added (e.g., due to collision).
        """
        # 如果当前节点已经是目标节点，则无需扩展
        # if np.array_equal(parent_node.q, q_sample):
        #     return None

        # 计算从当前父节点到目标节点的下一个扩展配置
        q_extend = extend_robot_state(
            parent_node.q,
            q_sample,
            self.options.max_connection_dist,  # 最大扩展距离
        )

        # 检查从当前父节点到扩展节点的路径是否无碰撞
        if not has_collision_free_path(
            parent_node.q,
            q_extend,
            self.options.max_step_size,  # 碰撞检测的步长
            self.model,
            self.collision_model,
            distance_padding=self.options.collision_distance_padding,  # 碰撞缓冲区
        ):
            return None  # 如果路径有碰撞，则不添加节点

        # 将扩展节点添加到树中
        new_node = self.add_node_to_tree(tree, q_extend, parent_node)

        # 返回新添加的节点
        return new_node

    def extract_path_from_trees(self, start_tree_final_node, goal_tree_final_node):
        """
        Extracts the final path from the RRT trees

        from the start tree root to the goal tree root passing through both final nodes.

        Parameters
        ----------
            start_tree_final_node : `pyroboplan.planning.graph.Node`
                The last node of the start tree.
            goal_tree_final_node : `pyroboplan.planning.graph.Node`, optional
                The last node of the goal tree.
                If None, this means the goal tree is ignored.

        Return
        ------
            list[array-like]
                A list of robot configurations describing the path waypoints in order.
        """
        # 回溯起始树的路径，从最终节点回溯到根节点
        path = retrace_path(start_tree_final_node)

        # 如果目标树的最终节点存在，则提取目标树的路径
        if goal_tree_final_node:
            # 从目标树的最终节点回溯路径，但不包括目标树的最终节点本身
            goal_tree_path = retrace_path(goal_tree_final_node.parent)
            # 反转目标树的路径，使其从目标树的根节点到最终节点
            goal_tree_path.reverse()
            # 将目标树的路径追加到起始树的路径后
            path += goal_tree_path

        # 将路径中的节点转换为关节配置（机器人状态）
        return [n.q for n in path]

    def add_node_to_tree(self, tree, q_new, parent_node):
        """
        Add a new node to the tree without rewiring.

        Parameters
        ----------
            tree : `pyroboplan.planning.graph.Graph`
                The tree to which to add the new node.
            q_new : array-like
                The robot configuration from which to create a new tree node.
            parent_node : `pyroboplan.planning.graph.Node`
                The parent node to connect the new node to.

        Returns
        -------
            `pyroboplan.planning.graph.Node`
                The new node that was added to the tree.
        """
        # Create the new node
        new_node = Node(q_new, parent=parent_node)
        # Add the new node to the tree
        tree.add_node(new_node)
        # Add an edge from the parent node to the new node
        edge = tree.add_edge(parent_node, new_node)
        # Calculate the cost of the new node
        new_node.cost = parent_node.cost + edge.cost
        
        return new_node

    def rewire_tree(self, tree, new_node):
        """
        Rewire the tree for RRT* algorithm.

        Parameters
        ----------
            tree : `pyroboplan.planning.graph.Graph`
                The tree to rewire.
            new_node : `pyroboplan.planning.graph.Node`
                The new node that was recently added to the tree.
        """
        if not self.options.rrt_star:
            return  # Do nothing if RRT* is not enabled

        min_cost = new_node.cost
        for other_node in tree.nodes:
            # Do not consider trivial nodes
            if other_node == new_node or other_node == new_node.parent:
                continue
            # Do not consider nodes farther than the configured rewire distance
            new_distance = configuration_distance(other_node.q, new_node.q)
            if new_distance > self.options.max_rewire_dist:
                continue
            # Rewire if this new connection would be of lower cost and is collision free
            new_cost = other_node.cost + new_distance
            if new_cost < min_cost:
                new_path = discretize_joint_space_path(
                    [new_node.q, other_node.q], self.options.max_step_size
                )
                if not check_collisions_along_path(
                    self.model,
                    self.collision_model,
                    new_path,
                    distance_padding=self.options.collision_distance_padding,
                ):
                    new_node.parent = other_node
                    new_node.cost = new_cost
                    tree.remove_edge(new_node.parent, new_node)
                    edge = tree.add_edge(other_node, new_node)
                    min_cost = new_cost
                    
                    # print(f"Rewiring node {new_node} \n to new node {other_node}")

            # Rewire the other node if this new connection would be of lower cost
            if new_cost + new_distance < other_node.cost:
                new_path = discretize_joint_space_path(
                    [new_node.q, other_node.q], self.options.max_step_size
                )
                if not check_collisions_along_path(
                    self.model,
                    self.collision_model,
                    new_path,
                    distance_padding=self.options.collision_distance_padding,
                ):
                    other_node.parent = new_node
                    other_node.cost = new_cost + new_distance
                    tree.remove_edge(other_node, other_node.parent)
                    edge = tree.add_edge(new_node, other_node)
                    
                    # print(f"Rewiring node {other_node} \n to new node {new_node}")

    def visualize(
        self,
        visualizer,
        frame_name,
        path_name="planned_path",
        tree_name="rrt",
        show_path=True,
        show_tree=False,
    ):
        """
        Visualizes the RRT path.

        Parameters
        ----------
            visualizer : `pinocchio.visualize.meshcat_visualizer.MeshcatVisualizer`, optional
                The visualizer to use for this solver.
            frame_name : str
                The name of the frame to use when visualizing paths in Cartesian space.
            path_name : str, optional
                The name of the MeshCat component for the path.
            tree_name : str, optional
                The name of the MeshCat component for the tree.
            show_path : bool, optional
                If true, shows the final path from start to goal.
            show_tree : bool, optional
                If true, shows the entire sampled tree.
        """
        visualizer.viewer[path_name].delete()
        if show_path:
            q_path = discretize_joint_space_path(
                self.latest_path, self.options.max_step_size
            )

        if show_tree:
            start_path_tforms = []
            for edge in self.start_tree.edges:
                q_path = discretize_joint_space_path(
                    [edge.nodeA.q, edge.nodeB.q], self.options.max_step_size
                )
                start_path_tforms.append(
                    extract_cartesian_poses(self.model, frame_name, q_path)
                )

            goal_path_tforms = []
            for edge in self.goal_tree.edges:
                q_path = discretize_joint_space_path(
                    [edge.nodeA.q, edge.nodeB.q], self.options.max_step_size
                )
                goal_path_tforms.append(
                    extract_cartesian_poses(self.model, frame_name, q_path)
                )
