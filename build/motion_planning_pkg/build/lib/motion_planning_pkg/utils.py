"""Core utilities for robot modeling."""

import copy
import numpy as np
import pinocchio


def check_collisions_at_state(
    model,
    collision_model,
    q,
    data=None,
    collision_data=None,
    distance_padding=0.0,
):
    """
    Checks whether a specified joint configuration is collision-free.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for collision checking.
        collision_model : `pinocchio.Model`
            The collision model to use for collision checking.
        q : array-like
            The joint configuration of the model.
        data : `pinocchio.Data`, optional
            The model data to use for collision checking. If None, data is created automatically.
        collision_data : `pinocchio.GeometryData`, optional
            The collision_model data to use for collision checking. If None, data is created automatically.
        distance_padding : float, optional
            The padding, in meters, to use for distance to nearest collision.

    Returns
    -------
        bool
            True if there are any collisions or minimum distance violations, otherwise False.
    """
    if not data:
        data = model.createData()
    if not collision_data:
        collision_data = collision_model.createData()
    stop_at_first_collision = True  # For faster computation

    for elem in collision_data.collisionRequests:
        elem.security_margin = distance_padding

    pinocchio.computeCollisions(
        model, data, collision_model, collision_data, q, stop_at_first_collision
    )
    
    # 检查碰撞结果并提取碰撞的 link 名称
    colliding_links = []
    for i, cr in enumerate(collision_data.collisionResults):
        if cr.isCollision():
            pair = collision_model.collisionPairs[i]
            link1 = collision_model.geometryObjects[pair.first].name
            link2 = collision_model.geometryObjects[pair.second].name
            colliding_links.append((link1, link2))

    # # 输出碰撞的 link 信息
    # if colliding_links:
    #     print("Collisions detected between the following links:")
    #     for link1, link2 in colliding_links:
    #         print(f"  - {link1} and {link2}")

    return len(colliding_links) > 0


def get_minimum_distance_at_state(
    model, collision_model, q, data=None, collision_data=None
):
    """
    Gets the minimum distance to collision at a specified state.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for collision checking.
        collision_model : `pinocchio.Model`
            The collision model to use for collision checking.
        q : array-like
            The joint configuration of the model.
        data : `pinocchio.Data`, optional
            The model data to use for collision checking. If None, data is created automatically.
        collision_data : `pinocchio.GeometryData`, optional
            The collision_model data to use for collision checking. If None, data is created automatically.

    Returns
    -------
        float
            The minimum distance to collision, in meters.
    """
    if len(collision_model.collisionPairs) == 0:
        return np.inf

    if not data:
        data = model.createData()
    if not collision_data:
        collision_data = collision_model.createData()

    pinocchio.computeDistances(model, data, collision_model, collision_data, q)
    return np.min([dr.min_distance for dr in collision_data.distanceResults])


def check_collisions_along_path(
    model, collision_model, q_path, data=None, collision_data=None, distance_padding=0.0
):
    """
    Checks whether a path consisting of multiple joint configurations is collision-free.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random state.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        q_path : list[array-like]
            A list of joint configurations describing the path.
        data : `pinocchio.Data`, optional
            The model data to use for collision checking. If None, data is created automatically.
        collision_data : `pinocchio.GeometryData`, optional
            The collision_model data to use for collision checking. If None, data is created automatically.
        distance_padding : float, optional
            The padding, in meters, to use for distance to nearest collision.

    Returns
    -------
        bool
            True if there are any collisions or minimum distance violations, otherwise False.
    """
    if not data:
        data = model.createData()
    if not collision_data:
        collision_data = collision_model.createData()
    stop_at_first_collision = True  # For faster computation

    for elem in collision_data.collisionRequests:
        elem.security_margin = distance_padding

    for q in q_path:
        pinocchio.computeCollisions(
            model, data, collision_model, collision_data, q, stop_at_first_collision
        )
        if np.any([cr.isCollision() for cr in collision_data.collisionResults]):
            return True

    return False


def configuration_distance(q_start, q_end):
    """
    Returns the distance between two joint configurations.

    Parameters
    ----------
        q_start : array-like
            The start joint configuration.
        q_end : array-like
            The end joint configuration.

    Returns
    -------
        float
            The distance between the two joint configurations.
    """
    return np.linalg.norm(q_end - q_start)


def get_path_length(q_path):
    """
    Returns the configuration distance of a path.

    Parameters
    ----------
        q_path : list[array-like]
            A list of joint configurations describing a path.

    Returns
    -------
        float
            The total configuration distance of the entire path.
    """
    total_distance = 0.0
    for idx in range(1, len(q_path)):
        total_distance += configuration_distance(q_path[idx - 1], q_path[idx])
    return total_distance


def get_random_state(model, padding=0.0):
    """
    Returns a random state that is within the model's joint limits.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random state.
        padding : float or array-like, optional
            The padding to use around the sampled joint limits.

    Returns
    -------
        array-like
            A set of randomly generated joint variables.
    """
    return np.random.uniform(
        model.lowerPositionLimit + padding, model.upperPositionLimit - padding
    )


def get_random_collision_free_state(
    model, collision_model, joint_padding=0.0, distance_padding=0.0, max_tries=100
):
    """
    Returns a random state that is within the model's joint limits and is collision-free according to the collision model.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random state.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        joint_padding : float or array-like, optional
            The padding to use around the sampled joint limits.
        distance_padding : float, optional
            The padding, in meters, to use for distance to nearest collision.
        max_tries : int, optional
            The maximum number of tries for sampling a collision-free state.

    Returns
    -------
        array-like
            A set of randomly generated collision-free joint variables, or None if one cannot be found.
    """
    num_tries = 0
    while num_tries < max_tries:
        state = get_random_state(model, padding=joint_padding)
        if not check_collisions_at_state(
            model, collision_model, state, distance_padding=distance_padding
        ):
            return state
        num_tries += 1

    print(f"Could not generate collision-free state after {max_tries} tries.")
    return None


def get_random_transform(model, target_frame, joint_padding=0.0):
    """
    Returns a random transform for a target frame that is within the model's joint limits.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random transform.
        target_frame : str
            The name of the frame for which to generate a random transform.
        joint_padding : float or array-like, optional
            The padding to use around the sampled joint limits.

    Returns
    -------
        `pinocchio.SE3`
            A randomly generated transform for the specified target frame.
    """
    q_target = get_random_state(model, joint_padding)
    data = model.createData()
    target_frame_id = model.getFrameId(target_frame)
    pinocchio.framesForwardKinematics(model, data, q_target)
    return data.oMf[target_frame_id]


def get_random_collision_free_transform(
    model,
    collision_model,
    target_frame,
    joint_padding=0.0,
    distance_padding=0.0,
    max_tries=100,
):
    """
    Returns a random transform for a target frame that is within the model's joint limits and is collision-free.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random transform.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        target_frame : str
            The name of the frame for which to generate a random transform.
        joint_padding : float or array-like, optional
            The padding to use around the sampled joint limits.
        distance_padding : float, optional
            The padding, in meters, to use for distance to nearest collision.
        max_tries : int, optional
            The maximum number of tries for sampling a collision-free state.

    Returns
    -------
        `pinocchio.SE3`
            A randomly generated transform for the specified target frame.
    """
    q_target = get_random_collision_free_state(
        model,
        collision_model,
        joint_padding=joint_padding,
        distance_padding=distance_padding,
        max_tries=max_tries,
    )
    data = model.createData()
    target_frame_id = model.getFrameId(target_frame)
    pinocchio.framesForwardKinematics(model, data, q_target)
    return data.oMf[target_frame_id]


def check_within_limits(model, q):
    """
    Checks whether a particular joint configuration is within the model's joint limits.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to generate a random state.
        q : array-like
            The joint configuration for the model.

    Returns
    -------
        bool
            True if the configuration is within joint limits, otherwise False.
    """
    return np.all(q >= model.lowerPositionLimit) and np.all(
        q <= model.upperPositionLimit
    )


def extract_cartesian_pose(model, target_frame, q, data=None):
    """
    Extracts the Cartesian pose of a specified model frame given a joint configuration.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to perform forward kinematics.
        target_frame : str
            The name of the target frame.
        q : array-like
            The joint configuration values describing the robot state.
        data : `pinocchio.Data`, optional
            The model data to use. If not set, one will be created.

    Returns
    -------
        `pinocchio.SE3`
            The transform describing the Cartesian pose of the specified frame at the provided joint configuration.
    """
    if data is None:
        data = model.createData()
    target_frame_id = model.getFrameId(target_frame)
    pinocchio.framesForwardKinematics(model, data, q)
    return copy.deepcopy(data.oMf[target_frame_id])


def extract_cartesian_poses(model, target_frame, q_vec, data=None):
    """
    Extracts the Cartesian poses of a specified model frame given a list of joint configurations.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model from which to perform forward kinematics.
        target_frame : str
            The name of the target frame.
        q_vec : array-like
            A list of joint configuration values describing the path.
        data : `pinocchio.Data`, optional
            The model data to use. If not set, one will be created.

    Returns
    -------
        list[`pinocchio.SE3`]
            A list of transforms describing the Cartesian poses of the specified frame at the provided joint configurations.
    """
    if data is None:
        data = model.createData()
    target_frame_id = model.getFrameId(target_frame)
    tforms = []
    for q in q_vec:
        pinocchio.framesForwardKinematics(model, data, q)
        tforms.append(copy.deepcopy(data.oMf[target_frame_id]))
    return tforms


def get_collision_geometry_ids(model, collision_model, body):
    """
    Gets a list of collision geometry model IDs for a specified body name.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for getting frame IDs.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        body : str
            The name of the body.
            This can be directly the name of a geometry in the collision model,
            or it can be the name of the frame in the main model.

    Return
    ------
        list[int]
            A list of collision geometry IDs corresponding to the body name.
    """
    body_collision_geom_ids = []

    # First, check if this is directly a collision geometry.
    body_collision_geom_id = collision_model.getGeometryId(body)
    if body_collision_geom_id < collision_model.ngeoms:
        body_collision_geom_ids.append(body_collision_geom_id)
    else:
        # Otherwise, look for the frame name in the model and return its associated collision objects.
        body_frame_id = model.getFrameId(body)
        if body_frame_id < model.nframes:
            for id, obj in enumerate(collision_model.geometryObjects):
                if obj.parentFrame == body_frame_id:
                    body_collision_geom_ids.append(id)

    return body_collision_geom_ids


def get_collision_pair_indices_from_bodies(model, collision_model, body_list):
    """
    Returns a list of all the collision pair indices involving a list of objects.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for getting frame IDs.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        body_list : list[str]
            A list containing the names of bodies.
            These can be directly the name of a geometry in the collision model,
            or they can be the name of the frame in the main model.

    Return
    ------
        list[int]
            The indices of the collision pairs list involving the bodies in the specified list.
    """
    collision_ids = set()
    for obj in body_list:
        ids = get_collision_geometry_ids(model, collision_model, obj)
        collision_ids.update(ids)

    pairs = []
    for idx, p in enumerate(collision_model.collisionPairs):
        if p.first in collision_ids or p.second in collision_ids:
            pairs.append(idx)

    return pairs


def set_collisions(model, collision_model, body1, body2, enable):
    """
    Sets collision checking between two bodies by searching for their corresponding geometry objects in the collision model.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for getting frame IDs.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        body1 : str
            The name of the first body.
        body2 : str
            The name of the second body.
        enable : bool
            If True, enables collisions. If False, disables collisions.
    """
    body1_collision_ids = get_collision_geometry_ids(model, collision_model, body1)
    body2_collision_ids = get_collision_geometry_ids(model, collision_model, body2)

    for id1 in body1_collision_ids:
        for id2 in body2_collision_ids:
            pair = pinocchio.CollisionPair(id1, id2)
            if enable:
                collision_model.addCollisionPair(pair)
            else:
                collision_model.removeCollisionPair(pair)


def calculate_collision_vector_and_jacobians(
    model, collision_model, data, collision_data, pair_idx, q
):
    """
    Given collision and distance results from collision model data, computes the collision vector
    and collision Jacobians at both collision points.

    This is useful for algorithms that perform collision avoidance, such as IK and trajectory optimization.

    Note that forward kinematics, collision, and distance checks must be evaluated first to populate the
    `data` and `collision_data` variables.

    Parameters
    ----------
        model : `pinocchio.Model`
            The model to use for Jacobian computation.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        data : `pinocchio.Data`
            The model data to use for Jacobian computation.
        collision_data : `pinocchio.GeometryData`
            The collision_model data to use for collision checking.
        pair_idx : int
            The index of the collision pair from which to extract information.
        q : array-like
            The joint configuration of the model.

    Return
    ------
        tuple(array-like, array-like, array-like)
            A tuple containing collision distance vector from frame1 to frame2,
            and the collision Jacobians at frame1 and frame2.
    """
    cp = collision_model.collisionPairs[pair_idx]
    dr = collision_data.distanceResults[pair_idx]

    # According to the coal documentation, the normal always points from object1 to object2.
    coll_points = [dr.getNearestPoint1(), dr.getNearestPoint2()]
    distance_vec = dr.normal
    distance = dr.min_distance

    # Calculate the Jacobians at the parent frames of both collision points.
    parent_frame1 = collision_model.geometryObjects[cp.first].parentFrame
    if parent_frame1 >= model.nframes:
        parent_frame1 = 0
    Jframe1 = pinocchio.computeFrameJacobian(
        model, data, q, parent_frame1, pinocchio.ReferenceFrame.LOCAL_WORLD_ALIGNED
    )
    r1 = coll_points[0] - data.oMf[parent_frame1].translation
    Jcoll1 = pinocchio.SE3(np.eye(3), r1).toActionMatrix()[:3, :] @ Jframe1

    parent_frame2 = collision_model.geometryObjects[cp.second].parentFrame
    if parent_frame2 >= model.nframes:
        parent_frame2 = 0
    Jframe2 = pinocchio.computeFrameJacobian(
        model, data, q, parent_frame2, pinocchio.ReferenceFrame.LOCAL_WORLD_ALIGNED
    )
    r2 = coll_points[1] - data.oMf[parent_frame2].translation
    Jcoll2 = pinocchio.SE3(np.eye(3), r2).toActionMatrix()[:3, :] @ Jframe2

    return distance_vec * distance, Jcoll1, Jcoll2

from itertools import product


def extend_robot_state(q_parent, q_sample, max_connection_distance):
    """
    Determines an incremental robot configuration between the parent and sample states, if one exists.

    Parameters
    ----------
        q_parent : array-like
            The starting robot configuration.
        q_sample : array-like
            The candidate sample configuration to extend towards.
        max_connection_distance : float
            Maximum angular distance, in radians, for connecting nodes.

    Returns
    -------
        array-like
            The resulting robot configuration, or None if it is not feasible.
    """
    q_diff = q_sample - q_parent
    distance = np.linalg.norm(q_diff)
    if distance == 0.0:
        return q_sample
    q_increment = max_connection_distance * q_diff / distance

    q_cur = q_parent
    # Clip the distance between nearest and sampled nodes to max connection distance.
    # If we have reached the sampled node, then we just check that.
    if configuration_distance(q_cur, q_sample) > max_connection_distance:
        q_extend = q_cur + q_increment
    else:
        q_extend = q_sample

    # Then there are no collisions so the extension is valid
    return q_extend


def has_collision_free_path(
    q1,
    q2,
    max_step_size,
    model,
    collision_model,
    data=None,
    collision_data=None,
    distance_padding=0.0,
):
    """
    Determines if there is a collision free path between the provided nodes and models.

    Parameters
    ----------
        q1 : array-like
            The starting robot configuration.
        q2 : array-like
            The destination robot configuration.
        max_step_size : float
            Maximum joint configuration step size for collision checking along path segments.
        model : `pinocchio.Model`
            The model for the robot configuration.
        collision_model : `pinocchio.Model`
            The model to use for collision checking.
        data : `pinocchio.Data`, optional
            The model data to use for this solver. If None, data is created automatically.
        collision_data : `pinocchio.GeometryData`, optional
            The collision_model data to use for this solver. If None, data is created automatically.
        distance_padding : float, optional
            The padding, in meters, to use for distance to nearest collision.

    Returns
    -------
        bool
            True if the configurations can be connected, False otherwise.
    """
    # Ensure the destination is collision free.
    if check_collisions_at_state(
        model,
        collision_model,
        q2,
        data,
        collision_data,
        distance_padding=distance_padding,
    ):
        return False

    # Ensure the discretized path is collision free.
    path_to_q_extend = discretize_joint_space_path([q1, q2], max_step_size)
    if check_collisions_along_path(
        model,
        collision_model,
        path_to_q_extend,
        data,
        collision_data,
        distance_padding=distance_padding,
    ):
        return False

    return True


def discretize_joint_space_path(q_path, max_angle_distance):
    """
    Discretizes a joint space path given a maximum angle distance between samples.

    This is used primarily for producing paths for collision checking.

    Parameters
    ----------
        q_path : list[array-like]
            A list of the joint configurations describing a path.
        max_angle_distance : float
            The maximum angular displacement, in radians, between samples.

    Returns
    -------
        list[array-like]
            A list of joint configuration arrays between the start and end points, inclusive.
    """
    q_discretized = []
    for idx in range(1, len(q_path)):
        q_start = q_path[idx - 1]
        q_end = q_path[idx]
        q_diff = q_end - q_start
        num_steps = int(np.ceil(np.linalg.norm(q_diff) / max_angle_distance)) + 1
        step_vec = np.linspace(0.0, 1.0, num_steps)
        q_discretized.extend([q_start + step * q_diff for step in step_vec])
    return q_discretized


def retrace_path(goal_node):
    """
    Retraces a path to the specified `goal_node` from a root node (a node with no parent).

    The resulting path will be returned in order form the start at index `0` to the `goal_node`
    at the index `-1`.

    Parameters
    ----------
        goal_node : `pyroboplan.planning.graph.Node`
            The starting joint configuration.

    Returns
    -------
        list[`pyroboplan.planning.graph.Node`]
            A list a nodes from the root to the specified `goal_node`.

    """
    path = []
    current = goal_node
    while current:
        path.append(current)
        current = current.parent
    path.reverse()
    return path


def discretized_joint_space_generator(model, step_size, generate_random=True):
    """
    Discretizes the entire joint space of the model at step_size increments.
    Once the entire space has been returned, the generator can optionally continue
    returning random samples from the configuration space - in which case this
    generator will never terminate.

    This is an extraordinarily expensive operation for high DOF manipulators
    and small step sizes!

    Parameters
    ----------
        model : `pinocchio.Model`
            The robot model containing lower and upper position limits.
        step_size : float
            The step size for sampling.
        generate_random : bool
            If True, continue randomly sampling the configuration space.
            Otherwise this generator will terminate.

    Yields
    ------
        np.ndarray
            The next point in the configuration space.
    """
    lower = model.lowerPositionLimit
    upper = model.upperPositionLimit

    # Ensure the range is inclusive of endpoints
    ranges = [np.arange(l, u + step_size, step_size) for l, u in zip(lower, upper)]
    for point in product(*ranges):
        yield np.array(point)

    # Once we have iterated through all available points we return random samples.
    while generate_random:
        yield get_random_state(model)
