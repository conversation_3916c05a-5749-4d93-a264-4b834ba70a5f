import rclpy
from rclpy.node import Node
from trajectory_msgs.msg import JointTrajectory, JointTrajectoryPoint
import mujoco,mujoco_viewer
import numpy as np


class TrajectoryFollow(Node):
    def __init__(self):
        super().__init__('trajectory_follow_node')
        
        # 声明参数，从launch文件中读取
        self.declare_parameter('mujoco_model_path', 
            '/home/<USER>/ccag/ccag_ws/src/motion_planning_pkg/motion_planning_pkg/resources/panda_description/mjcf/scene.xml')
        self.declare_parameter('dt', 0.001)
        
        # 获取参数值
        self.mujoco_model_path = self.get_parameter('mujoco_model_path').get_parameter_value().string_value
        self.dt = self.get_parameter('dt').get_parameter_value().double_value
        
        # 打印参数值用于调试
        self.get_logger().info(f'MuJoCo model path: {self.mujoco_model_path}')
        self.get_logger().info(f'Simulation timestep (dt): {self.dt}')
        
        # Subscriber for end-effector pose
        self.traj_subscription = self.create_subscription(
            JointTrajectory,
            '/generated_trajectory',
            self.traj_callback,
            10
        )
        # self.traj_gen = None
    def traj_callback(self, msg):
        # self.traj_gen = msg
        self.get_logger().info("Trajectory received and running in MuJoCo...")
        self.run_mujoco(msg)

    def run_mujoco(self, traj_msg):
        """
        traj_msg: JointTrajectory ROS2消息
        """
        print("\nRunning MuJoCo simulation...")
        if traj_msg is None:
            self.get_logger().error("No trajectory received. Cannot run simulation.")
            return

        # 解析时间点
        time_points = []
        for pt in traj_msg.points:
            t = pt.time_from_start.sec + pt.time_from_start.nanosec * 1e-9
            time_points.append(t)
        time_points = np.array(time_points)

        # 解析关节角度
        q_vec = []
        for pt in traj_msg.points:
            q_vec.append(pt.positions)
        q_vec = np.array(q_vec).T  # shape: [num_joints, num_points]

        if q_vec.size == 0 or time_points.size == 0:
            self.get_logger().error("Trajectory message is empty.")
            return

        num_points = q_vec.shape[1]

        # 后续代码与原来一致
        model = mujoco.MjModel.from_xml_path(self.mujoco_model_path)
        model.opt.timestep = self.dt
        data = mujoco.MjData(model)
        viewer = mujoco_viewer.MujocoViewer(model, data)

        for idx in range(num_points - 1):
            data.qpos[:7] = q_vec[:7, idx]
            mujoco.mj_forward(model, data)
            time_interval = time_points[idx + 1] - time_points[idx]
            steps = int(time_interval / self.dt)
            for _ in range(steps):
                if _ == 0:
                    mujoco.mj_step(model, data)
                viewer.render()

        while viewer.is_alive:
            viewer.render()
        viewer.close()
        
def main():    
    rclpy.init()
    planner = TrajectoryFollow()
    rclpy.spin(planner)
    rclpy.shutdown()
    
if __name__ == '__main__':
    main()