{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Release-39a20bd589d07d54e97c.json", "minimumCMakeVersion": {"string": "3.12"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1, 2]}], "name": "Release", "projects": [{"directoryIndexes": [0], "name": "pose_estimation", "targetIndexes": [0, 1, 2]}], "targets": [{"directoryIndex": 0, "id": "pose_estimation::@6890427a1f51a3e7e1df", "jsonFile": "target-pose_estimation-Release-e843ac1f3eec1b315ea6.json", "name": "pose_estimation", "projectIndex": 0}, {"directoryIndex": 0, "id": "pose_estimation_uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-pose_estimation_uninstall-Release-77fa39ac539a61e7830d.json", "name": "pose_estimation_uninstall", "projectIndex": 0}, {"directoryIndex": 0, "id": "uninstall::@6890427a1f51a3e7e1df", "jsonFile": "target-uninstall-Release-4e283bf2439200763a0f.json", "name": "uninstall", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/ccag/ccag_ws/build/pose_estimation", "source": "/home/<USER>/ccag/ccag_ws/src/pose_estimation"}, "version": {"major": 2, "minor": 8}}