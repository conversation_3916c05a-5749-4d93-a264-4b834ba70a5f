{"cmake": {"generator": {"multiConfig": false, "name": "Unix Makefiles"}, "paths": {"cmake": "/usr/local/bin/cmake", "cpack": "/usr/local/bin/cpack", "ctest": "/usr/local/bin/ctest", "root": "/usr/local/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-3e834f063b0c7159f4d8.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}], "reply": {"client-colcon-cmake": {"codemodel-v2": {"jsonFile": "codemodel-v2-3e834f063b0c7159f4d8.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}}}}