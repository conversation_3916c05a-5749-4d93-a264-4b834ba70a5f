// generated from rosidl_adapter/resource/msg.idl.em
// with input from data_interface/msg/PoseEst3d2dPointsMsg.msg
// generated code does not contain a copyright notice

#include "data_interface/msg/PoseEst3d2dPointMsg.idl"

module data_interface {
  module msg {
    typedef data_interface::msg::PoseEst3d2dPointMsg data_interface__msg__PoseEst3d2dPointMsg;
    typedef data_interface__msg__PoseEst3d2dPointMsg data_interface__msg__PoseEst3d2dPointMsg__5[5];
    @verbatim (language="comment", text=
      "PoseEst3d2dPointsMsg.msg")
    struct PoseEst3d2dPointsMsg {
      data_interface__msg__PoseEst3d2dPointMsg__5 points;
    };
  };
};
