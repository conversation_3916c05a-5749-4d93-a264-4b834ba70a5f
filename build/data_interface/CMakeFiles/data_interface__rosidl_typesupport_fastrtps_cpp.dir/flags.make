# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# compile CXX with /usr/bin/c++
CXX_DEFINES = -DRCUTILS_ENABLE_FAULT_INJECTION -DROSIDL_TYPESUPPORT_FASTRTPS_CPP_BUILDING_DLL_data_interface -DROS_PACKAGE_NAME=\"data_interface\"

CXX_INCLUDES = -I/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_cpp -I/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_cpp -isystem /opt/ros/humble/include/fastcdr -isystem /opt/ros/humble/include/rmw -isystem /opt/ros/humble/include/rcutils -isystem /opt/ros/humble/include/rosidl_runtime_c -isystem /opt/ros/humble/include/rosidl_typesupport_interface -isystem /opt/ros/humble/include/rosidl_runtime_cpp -isystem /opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp -isystem /opt/ros/humble/include/sensor_msgs -isystem /opt/ros/humble/include/builtin_interfaces -isystem /opt/ros/humble/include/geometry_msgs -isystem /opt/ros/humble/include/std_msgs

CXX_FLAGS = -fPIC -Wall -Wextra -Wpedantic -Wredundant-decls -std=gnu++14

