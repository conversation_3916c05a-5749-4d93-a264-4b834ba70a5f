// generated from rosidl_generator_py/resource/_idl_support.c.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <stdbool.h>
#ifndef _WIN32
# pragma GCC diagnostic push
# pragma GCC diagnostic ignored "-Wunused-function"
#endif
#include "numpy/ndarrayobject.h"
#ifndef _WIN32
# pragma GCC diagnostic pop
#endif
#include "rosidl_runtime_c/visibility_control.h"
#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.h"
#include "data_interface/msg/detail/pose_est3d2d_points_msg__functions.h"

#include "rosidl_runtime_c/primitives_sequence.h"
#include "rosidl_runtime_c/primitives_sequence_functions.h"

// Nested array functions includes
#include "data_interface/msg/detail/pose_est3d2d_point_msg__functions.h"
// end nested array functions include
bool data_interface__msg__pose_est3d2d_point_msg__convert_from_py(PyObject * _pymsg, void * _ros_message);
PyObject * data_interface__msg__pose_est3d2d_point_msg__convert_to_py(void * raw_ros_message);

ROSIDL_GENERATOR_C_EXPORT
bool data_interface__msg__pose_est3d2d_points_msg__convert_from_py(PyObject * _pymsg, void * _ros_message)
{
  // check that the passed message is of the expected Python class
  {
    char full_classname_dest[65];
    {
      char * class_name = NULL;
      char * module_name = NULL;
      {
        PyObject * class_attr = PyObject_GetAttrString(_pymsg, "__class__");
        if (class_attr) {
          PyObject * name_attr = PyObject_GetAttrString(class_attr, "__name__");
          if (name_attr) {
            class_name = (char *)PyUnicode_1BYTE_DATA(name_attr);
            Py_DECREF(name_attr);
          }
          PyObject * module_attr = PyObject_GetAttrString(class_attr, "__module__");
          if (module_attr) {
            module_name = (char *)PyUnicode_1BYTE_DATA(module_attr);
            Py_DECREF(module_attr);
          }
          Py_DECREF(class_attr);
        }
      }
      if (!class_name || !module_name) {
        return false;
      }
      snprintf(full_classname_dest, sizeof(full_classname_dest), "%s.%s", module_name, class_name);
    }
    assert(strncmp("data_interface.msg._pose_est3d2d_points_msg.PoseEst3d2dPointsMsg", full_classname_dest, 64) == 0);
  }
  data_interface__msg__PoseEst3d2dPointsMsg * ros_message = _ros_message;
  {  // points
    PyObject * field = PyObject_GetAttrString(_pymsg, "points");
    if (!field) {
      return false;
    }
    PyObject * seq_field = PySequence_Fast(field, "expected a sequence in 'points'");
    if (!seq_field) {
      Py_DECREF(field);
      return false;
    }
    Py_ssize_t size = 5;
    data_interface__msg__PoseEst3d2dPointMsg * dest = ros_message->points;
    for (Py_ssize_t i = 0; i < size; ++i) {
      if (!data_interface__msg__pose_est3d2d_point_msg__convert_from_py(PySequence_Fast_GET_ITEM(seq_field, i), &dest[i])) {
        Py_DECREF(seq_field);
        Py_DECREF(field);
        return false;
      }
    }
    Py_DECREF(seq_field);
    Py_DECREF(field);
  }

  return true;
}

ROSIDL_GENERATOR_C_EXPORT
PyObject * data_interface__msg__pose_est3d2d_points_msg__convert_to_py(void * raw_ros_message)
{
  /* NOTE(esteve): Call constructor of PoseEst3d2dPointsMsg */
  PyObject * _pymessage = NULL;
  {
    PyObject * pymessage_module = PyImport_ImportModule("data_interface.msg._pose_est3d2d_points_msg");
    assert(pymessage_module);
    PyObject * pymessage_class = PyObject_GetAttrString(pymessage_module, "PoseEst3d2dPointsMsg");
    assert(pymessage_class);
    Py_DECREF(pymessage_module);
    _pymessage = PyObject_CallObject(pymessage_class, NULL);
    Py_DECREF(pymessage_class);
    if (!_pymessage) {
      return NULL;
    }
  }
  data_interface__msg__PoseEst3d2dPointsMsg * ros_message = (data_interface__msg__PoseEst3d2dPointsMsg *)raw_ros_message;
  {  // points
    PyObject * field = NULL;
    size_t size = 5;
    field = PyList_New(size);
    if (!field) {
      return NULL;
    }
    data_interface__msg__PoseEst3d2dPointMsg * item;
    for (size_t i = 0; i < size; ++i) {
      item = &(ros_message->points[i]);
      PyObject * pyitem = data_interface__msg__pose_est3d2d_point_msg__convert_to_py(item);
      if (!pyitem) {
        Py_DECREF(field);
        return NULL;
      }
      int rc = PyList_SetItem(field, i, pyitem);
      (void)rc;
      assert(rc == 0);
    }
    assert(PySequence_Check(field));
    {
      int rc = PyObject_SetAttrString(_pymessage, "points", field);
      Py_DECREF(field);
      if (rc) {
        return NULL;
      }
    }
  }

  // ownership of _pymessage is transferred to the caller
  return _pymessage;
}
