// generated from rosidl_typesupport_introspection_cpp/resource/idl__type_support.cpp.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#include "array"
#include "cstddef"
#include "string"
#include "vector"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_interface/macros.h"
#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.hpp"
#include "rosidl_typesupport_introspection_cpp/field_types.hpp"
#include "rosidl_typesupport_introspection_cpp/identifier.hpp"
#include "rosidl_typesupport_introspection_cpp/message_introspection.hpp"
#include "rosidl_typesupport_introspection_cpp/message_type_support_decl.hpp"
#include "rosidl_typesupport_introspection_cpp/visibility_control.h"

namespace data_interface
{

namespace msg
{

namespace rosidl_typesupport_introspection_cpp
{

void PoseEst3d2dPointsMsg_init_function(
  void * message_memory, rosidl_runtime_cpp::MessageInitialization _init)
{
  new (message_memory) data_interface::msg::PoseEst3d2dPointsMsg(_init);
}

void PoseEst3d2dPointsMsg_fini_function(void * message_memory)
{
  auto typed_message = static_cast<data_interface::msg::PoseEst3d2dPointsMsg *>(message_memory);
  typed_message->~PoseEst3d2dPointsMsg();
}

size_t size_function__PoseEst3d2dPointsMsg__points(const void * untyped_member)
{
  (void)untyped_member;
  return 5;
}

const void * get_const_function__PoseEst3d2dPointsMsg__points(const void * untyped_member, size_t index)
{
  const auto & member =
    *reinterpret_cast<const std::array<data_interface::msg::PoseEst3d2dPointMsg, 5> *>(untyped_member);
  return &member[index];
}

void * get_function__PoseEst3d2dPointsMsg__points(void * untyped_member, size_t index)
{
  auto & member =
    *reinterpret_cast<std::array<data_interface::msg::PoseEst3d2dPointMsg, 5> *>(untyped_member);
  return &member[index];
}

void fetch_function__PoseEst3d2dPointsMsg__points(
  const void * untyped_member, size_t index, void * untyped_value)
{
  const auto & item = *reinterpret_cast<const data_interface::msg::PoseEst3d2dPointMsg *>(
    get_const_function__PoseEst3d2dPointsMsg__points(untyped_member, index));
  auto & value = *reinterpret_cast<data_interface::msg::PoseEst3d2dPointMsg *>(untyped_value);
  value = item;
}

void assign_function__PoseEst3d2dPointsMsg__points(
  void * untyped_member, size_t index, const void * untyped_value)
{
  auto & item = *reinterpret_cast<data_interface::msg::PoseEst3d2dPointMsg *>(
    get_function__PoseEst3d2dPointsMsg__points(untyped_member, index));
  const auto & value = *reinterpret_cast<const data_interface::msg::PoseEst3d2dPointMsg *>(untyped_value);
  item = value;
}

static const ::rosidl_typesupport_introspection_cpp::MessageMember PoseEst3d2dPointsMsg_message_member_array[1] = {
  {
    "points",  // name
    ::rosidl_typesupport_introspection_cpp::ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    ::rosidl_typesupport_introspection_cpp::get_message_type_support_handle<data_interface::msg::PoseEst3d2dPointMsg>(),  // members of sub message
    true,  // is array
    5,  // array size
    false,  // is upper bound
    offsetof(data_interface::msg::PoseEst3d2dPointsMsg, points),  // bytes offset in struct
    nullptr,  // default value
    size_function__PoseEst3d2dPointsMsg__points,  // size() function pointer
    get_const_function__PoseEst3d2dPointsMsg__points,  // get_const(index) function pointer
    get_function__PoseEst3d2dPointsMsg__points,  // get(index) function pointer
    fetch_function__PoseEst3d2dPointsMsg__points,  // fetch(index, &value) function pointer
    assign_function__PoseEst3d2dPointsMsg__points,  // assign(index, value) function pointer
    nullptr  // resize(index) function pointer
  }
};

static const ::rosidl_typesupport_introspection_cpp::MessageMembers PoseEst3d2dPointsMsg_message_members = {
  "data_interface::msg",  // message namespace
  "PoseEst3d2dPointsMsg",  // message name
  1,  // number of fields
  sizeof(data_interface::msg::PoseEst3d2dPointsMsg),
  PoseEst3d2dPointsMsg_message_member_array,  // message members
  PoseEst3d2dPointsMsg_init_function,  // function to initialize message memory (memory has to be allocated)
  PoseEst3d2dPointsMsg_fini_function  // function to terminate message instance (will not free memory)
};

static const rosidl_message_type_support_t PoseEst3d2dPointsMsg_message_type_support_handle = {
  ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  &PoseEst3d2dPointsMsg_message_members,
  get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_introspection_cpp

}  // namespace msg

}  // namespace data_interface


namespace rosidl_typesupport_introspection_cpp
{

template<>
ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<data_interface::msg::PoseEst3d2dPointsMsg>()
{
  return &::data_interface::msg::rosidl_typesupport_introspection_cpp::PoseEst3d2dPointsMsg_message_type_support_handle;
}

}  // namespace rosidl_typesupport_introspection_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_INTROSPECTION_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, data_interface, msg, PoseEst3d2dPointsMsg)() {
  return &::data_interface::msg::rosidl_typesupport_introspection_cpp::PoseEst3d2dPointsMsg_message_type_support_handle;
}

#ifdef __cplusplus
}
#endif
