# generated from rosidl_generator_py/resource/_idl.py.em
# with input from data_interface:srv/PoseEst3d2dSrv.idl
# generated code does not contain a copyright notice


# Import statements for member types

import builtins  # noqa: E402, I100

import rosidl_parser.definition  # noqa: E402, I100


class Metaclass_PoseEst3d2dSrv_Request(type):
    """Metaclass of message 'PoseEst3d2dSrv_Request'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('data_interface')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'data_interface.srv.PoseEst3d2dSrv_Request')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__srv__pose_est3d2d_srv__request
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__srv__pose_est3d2d_srv__request
            cls._CONVERT_TO_PY = module.convert_to_py_msg__srv__pose_est3d2d_srv__request
            cls._TYPE_SUPPORT = module.type_support_msg__srv__pose_est3d2d_srv__request
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__srv__pose_est3d2d_srv__request

            from data_interface.msg import PoseEst3d2dPointsMsg
            if PoseEst3d2dPointsMsg.__class__._TYPE_SUPPORT is None:
                PoseEst3d2dPointsMsg.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class PoseEst3d2dSrv_Request(metaclass=Metaclass_PoseEst3d2dSrv_Request):
    """Message class 'PoseEst3d2dSrv_Request'."""

    __slots__ = [
        '_points',
    ]

    _fields_and_field_types = {
        'points': 'data_interface/PoseEst3d2dPointsMsg',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['data_interface', 'msg'], 'PoseEst3d2dPointsMsg'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from data_interface.msg import PoseEst3d2dPointsMsg
        self.points = kwargs.get('points', PoseEst3d2dPointsMsg())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.points != other.points:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def points(self):
        """Message field 'points'."""
        return self._points

    @points.setter
    def points(self, value):
        if __debug__:
            from data_interface.msg import PoseEst3d2dPointsMsg
            assert \
                isinstance(value, PoseEst3d2dPointsMsg), \
                "The 'points' field must be a sub message of type 'PoseEst3d2dPointsMsg'"
        self._points = value


# Import statements for member types

# already imported above
# import builtins

# already imported above
# import rosidl_parser.definition


class Metaclass_PoseEst3d2dSrv_Response(type):
    """Metaclass of message 'PoseEst3d2dSrv_Response'."""

    _CREATE_ROS_MESSAGE = None
    _CONVERT_FROM_PY = None
    _CONVERT_TO_PY = None
    _DESTROY_ROS_MESSAGE = None
    _TYPE_SUPPORT = None

    __constants = {
    }

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('data_interface')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'data_interface.srv.PoseEst3d2dSrv_Response')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._CREATE_ROS_MESSAGE = module.create_ros_message_msg__srv__pose_est3d2d_srv__response
            cls._CONVERT_FROM_PY = module.convert_from_py_msg__srv__pose_est3d2d_srv__response
            cls._CONVERT_TO_PY = module.convert_to_py_msg__srv__pose_est3d2d_srv__response
            cls._TYPE_SUPPORT = module.type_support_msg__srv__pose_est3d2d_srv__response
            cls._DESTROY_ROS_MESSAGE = module.destroy_ros_message_msg__srv__pose_est3d2d_srv__response

            from geometry_msgs.msg import PoseStamped
            if PoseStamped.__class__._TYPE_SUPPORT is None:
                PoseStamped.__class__.__import_type_support__()

    @classmethod
    def __prepare__(cls, name, bases, **kwargs):
        # list constant names here so that they appear in the help text of
        # the message class under "Data and other attributes defined here:"
        # as well as populate each message instance
        return {
        }


class PoseEst3d2dSrv_Response(metaclass=Metaclass_PoseEst3d2dSrv_Response):
    """Message class 'PoseEst3d2dSrv_Response'."""

    __slots__ = [
        '_pose',
    ]

    _fields_and_field_types = {
        'pose': 'geometry_msgs/PoseStamped',
    }

    SLOT_TYPES = (
        rosidl_parser.definition.NamespacedType(['geometry_msgs', 'msg'], 'PoseStamped'),  # noqa: E501
    )

    def __init__(self, **kwargs):
        assert all('_' + key in self.__slots__ for key in kwargs.keys()), \
            'Invalid arguments passed to constructor: %s' % \
            ', '.join(sorted(k for k in kwargs.keys() if '_' + k not in self.__slots__))
        from geometry_msgs.msg import PoseStamped
        self.pose = kwargs.get('pose', PoseStamped())

    def __repr__(self):
        typename = self.__class__.__module__.split('.')
        typename.pop()
        typename.append(self.__class__.__name__)
        args = []
        for s, t in zip(self.__slots__, self.SLOT_TYPES):
            field = getattr(self, s)
            fieldstr = repr(field)
            # We use Python array type for fields that can be directly stored
            # in them, and "normal" sequences for everything else.  If it is
            # a type that we store in an array, strip off the 'array' portion.
            if (
                isinstance(t, rosidl_parser.definition.AbstractSequence) and
                isinstance(t.value_type, rosidl_parser.definition.BasicType) and
                t.value_type.typename in ['float', 'double', 'int8', 'uint8', 'int16', 'uint16', 'int32', 'uint32', 'int64', 'uint64']
            ):
                if len(field) == 0:
                    fieldstr = '[]'
                else:
                    assert fieldstr.startswith('array(')
                    prefix = "array('X', "
                    suffix = ')'
                    fieldstr = fieldstr[len(prefix):-len(suffix)]
            args.append(s[1:] + '=' + fieldstr)
        return '%s(%s)' % ('.'.join(typename), ', '.join(args))

    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        if self.pose != other.pose:
            return False
        return True

    @classmethod
    def get_fields_and_field_types(cls):
        from copy import copy
        return copy(cls._fields_and_field_types)

    @builtins.property
    def pose(self):
        """Message field 'pose'."""
        return self._pose

    @pose.setter
    def pose(self, value):
        if __debug__:
            from geometry_msgs.msg import PoseStamped
            assert \
                isinstance(value, PoseStamped), \
                "The 'pose' field must be a sub message of type 'PoseStamped'"
        self._pose = value


class Metaclass_PoseEst3d2dSrv(type):
    """Metaclass of service 'PoseEst3d2dSrv'."""

    _TYPE_SUPPORT = None

    @classmethod
    def __import_type_support__(cls):
        try:
            from rosidl_generator_py import import_type_support
            module = import_type_support('data_interface')
        except ImportError:
            import logging
            import traceback
            logger = logging.getLogger(
                'data_interface.srv.PoseEst3d2dSrv')
            logger.debug(
                'Failed to import needed modules for type support:\n' +
                traceback.format_exc())
        else:
            cls._TYPE_SUPPORT = module.type_support_srv__srv__pose_est3d2d_srv

            from data_interface.srv import _pose_est3d2d_srv
            if _pose_est3d2d_srv.Metaclass_PoseEst3d2dSrv_Request._TYPE_SUPPORT is None:
                _pose_est3d2d_srv.Metaclass_PoseEst3d2dSrv_Request.__import_type_support__()
            if _pose_est3d2d_srv.Metaclass_PoseEst3d2dSrv_Response._TYPE_SUPPORT is None:
                _pose_est3d2d_srv.Metaclass_PoseEst3d2dSrv_Response.__import_type_support__()


class PoseEst3d2dSrv(metaclass=Metaclass_PoseEst3d2dSrv):
    from data_interface.srv._pose_est3d2d_srv import PoseEst3d2dSrv_Request as Request
    from data_interface.srv._pose_est3d2d_srv import PoseEst3d2dSrv_Response as Response

    def __init__(self):
        raise NotImplementedError('Service classes can not be instantiated')
