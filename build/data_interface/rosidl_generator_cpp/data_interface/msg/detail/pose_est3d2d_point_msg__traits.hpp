// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__TRAITS_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace data_interface
{

namespace msg
{

inline void to_flow_style_yaml(
  const PoseEst3d2dPointMsg & msg,
  std::ostream & out)
{
  out << "{";
  // member: x
  {
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << ", ";
  }

  // member: y
  {
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << ", ";
  }

  // member: label
  {
    out << "label: ";
    rosidl_generator_traits::value_to_yaml(msg.label, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PoseEst3d2dPointMsg & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: x
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "x: ";
    rosidl_generator_traits::value_to_yaml(msg.x, out);
    out << "\n";
  }

  // member: y
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "y: ";
    rosidl_generator_traits::value_to_yaml(msg.y, out);
    out << "\n";
  }

  // member: label
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "label: ";
    rosidl_generator_traits::value_to_yaml(msg.label, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PoseEst3d2dPointMsg & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace msg

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::msg::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::msg::PoseEst3d2dPointMsg & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::msg::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::msg::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::msg::PoseEst3d2dPointMsg & msg)
{
  return data_interface::msg::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::msg::PoseEst3d2dPointMsg>()
{
  return "data_interface::msg::PoseEst3d2dPointMsg";
}

template<>
inline const char * name<data_interface::msg::PoseEst3d2dPointMsg>()
{
  return "data_interface/msg/PoseEst3d2dPointMsg";
}

template<>
struct has_fixed_size<data_interface::msg::PoseEst3d2dPointMsg>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<data_interface::msg::PoseEst3d2dPointMsg>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<data_interface::msg::PoseEst3d2dPointMsg>
  : std::true_type {};

}  // namespace rosidl_generator_traits

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__TRAITS_HPP_
