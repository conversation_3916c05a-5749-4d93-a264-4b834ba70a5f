// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointsMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__BUILDER_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace data_interface
{

namespace msg
{

namespace builder
{

class Init_PoseEst3d2dPointsMsg_points
{
public:
  Init_PoseEst3d2dPointsMsg_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::msg::PoseEst3d2dPointsMsg points(::data_interface::msg::PoseEst3d2dPointsMsg::_points_type arg)
  {
    msg_.points = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::msg::PoseEst3d2dPointsMsg msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::msg::PoseEst3d2dPointsMsg>()
{
  return data_interface::msg::builder::Init_PoseEst3d2dPointsMsg_points();
}

}  // namespace data_interface

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINTS_MSG__BUILDER_HPP_
