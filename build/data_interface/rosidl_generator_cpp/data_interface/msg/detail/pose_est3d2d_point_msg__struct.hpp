// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__data_interface__msg__PoseEst3d2dPointMsg __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__msg__PoseEst3d2dPointMsg __declspec(deprecated)
#endif

namespace data_interface
{

namespace msg
{

// message struct
template<class ContainerAllocator>
struct PoseEst3d2dPointMsg_
{
  using Type = PoseEst3d2dPointMsg_<ContainerAllocator>;

  explicit PoseEst3d2dPointMsg_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0;
      this->y = 0.0;
      this->label = 0;
    }
  }

  explicit PoseEst3d2dPointMsg_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    (void)_alloc;
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->x = 0.0;
      this->y = 0.0;
      this->label = 0;
    }
  }

  // field types and members
  using _x_type =
    double;
  _x_type x;
  using _y_type =
    double;
  _y_type y;
  using _label_type =
    int8_t;
  _label_type label;

  // setters for named parameter idiom
  Type & set__x(
    const double & _arg)
  {
    this->x = _arg;
    return *this;
  }
  Type & set__y(
    const double & _arg)
  {
    this->y = _arg;
    return *this;
  }
  Type & set__label(
    const int8_t & _arg)
  {
    this->label = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__msg__PoseEst3d2dPointMsg
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__msg__PoseEst3d2dPointMsg
    std::shared_ptr<data_interface::msg::PoseEst3d2dPointMsg_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PoseEst3d2dPointMsg_ & other) const
  {
    if (this->x != other.x) {
      return false;
    }
    if (this->y != other.y) {
      return false;
    }
    if (this->label != other.label) {
      return false;
    }
    return true;
  }
  bool operator!=(const PoseEst3d2dPointMsg_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PoseEst3d2dPointMsg_

// alias to use template instance with default allocator
using PoseEst3d2dPointMsg =
  data_interface::msg::PoseEst3d2dPointMsg_<std::allocator<void>>;

// constant definitions

}  // namespace msg

}  // namespace data_interface

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__STRUCT_HPP_
