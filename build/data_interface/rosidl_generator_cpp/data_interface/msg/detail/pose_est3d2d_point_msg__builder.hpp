// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__BUILDER_HPP_
#define DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace data_interface
{

namespace msg
{

namespace builder
{

class Init_PoseEst3d2dPointMsg_label
{
public:
  explicit Init_PoseEst3d2dPointMsg_label(::data_interface::msg::PoseEst3d2dPointMsg & msg)
  : msg_(msg)
  {}
  ::data_interface::msg::PoseEst3d2dPointMsg label(::data_interface::msg::PoseEst3d2dPointMsg::_label_type arg)
  {
    msg_.label = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::msg::PoseEst3d2dPointMsg msg_;
};

class Init_PoseEst3d2dPointMsg_y
{
public:
  explicit Init_PoseEst3d2dPointMsg_y(::data_interface::msg::PoseEst3d2dPointMsg & msg)
  : msg_(msg)
  {}
  Init_PoseEst3d2dPointMsg_label y(::data_interface::msg::PoseEst3d2dPointMsg::_y_type arg)
  {
    msg_.y = std::move(arg);
    return Init_PoseEst3d2dPointMsg_label(msg_);
  }

private:
  ::data_interface::msg::PoseEst3d2dPointMsg msg_;
};

class Init_PoseEst3d2dPointMsg_x
{
public:
  Init_PoseEst3d2dPointMsg_x()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  Init_PoseEst3d2dPointMsg_y x(::data_interface::msg::PoseEst3d2dPointMsg::_x_type arg)
  {
    msg_.x = std::move(arg);
    return Init_PoseEst3d2dPointMsg_y(msg_);
  }

private:
  ::data_interface::msg::PoseEst3d2dPointMsg msg_;
};

}  // namespace builder

}  // namespace msg

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::msg::PoseEst3d2dPointMsg>()
{
  return data_interface::msg::builder::Init_PoseEst3d2dPointMsg_x();
}

}  // namespace data_interface

#endif  // DATA_INTERFACE__MSG__DETAIL__POSE_EST3D2D_POINT_MSG__BUILDER_HPP_
