// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from data_interface:srv/PoseEst3d2dSrv.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__BUILDER_HPP_
#define DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "data_interface/srv/detail/pose_est3d2d_srv__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_PoseEst3d2dSrv_Request_points
{
public:
  Init_PoseEst3d2dSrv_Request_points()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::PoseEst3d2dSrv_Request points(::data_interface::srv::PoseEst3d2dSrv_Request::_points_type arg)
  {
    msg_.points = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::PoseEst3d2dSrv_Request msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::PoseEst3d2dSrv_Request>()
{
  return data_interface::srv::builder::Init_PoseEst3d2dSrv_Request_points();
}

}  // namespace data_interface


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_PoseEst3d2dSrv_Response_pose
{
public:
  Init_PoseEst3d2dSrv_Response_pose()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::PoseEst3d2dSrv_Response pose(::data_interface::srv::PoseEst3d2dSrv_Response::_pose_type arg)
  {
    msg_.pose = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::PoseEst3d2dSrv_Response msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::PoseEst3d2dSrv_Response>()
{
  return data_interface::srv::builder::Init_PoseEst3d2dSrv_Response_pose();
}

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__BUILDER_HPP_
