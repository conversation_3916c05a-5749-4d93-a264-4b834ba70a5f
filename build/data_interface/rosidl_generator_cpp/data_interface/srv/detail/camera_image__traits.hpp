// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from data_interface:srv/CameraImage.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__TRAITS_HPP_
#define DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "data_interface/srv/detail/camera_image__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const CameraImage_Request & msg,
  std::ostream & out)
{
  out << "{";
  // member: get
  {
    out << "get: ";
    rosidl_generator_traits::value_to_yaml(msg.get, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const CameraImage_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: get
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "get: ";
    rosidl_generator_traits::value_to_yaml(msg.get, out);
    out << "\n";
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const CameraImage_Request & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::CameraImage_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::CameraImage_Request & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::CameraImage_Request>()
{
  return "data_interface::srv::CameraImage_Request";
}

template<>
inline const char * name<data_interface::srv::CameraImage_Request>()
{
  return "data_interface/srv/CameraImage_Request";
}

template<>
struct has_fixed_size<data_interface::srv::CameraImage_Request>
  : std::integral_constant<bool, true> {};

template<>
struct has_bounded_size<data_interface::srv::CameraImage_Request>
  : std::integral_constant<bool, true> {};

template<>
struct is_message<data_interface::srv::CameraImage_Request>
  : std::true_type {};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'server_image'
#include "sensor_msgs/msg/detail/image__traits.hpp"

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const CameraImage_Response & msg,
  std::ostream & out)
{
  out << "{";
  // member: server_image
  {
    out << "server_image: ";
    to_flow_style_yaml(msg.server_image, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const CameraImage_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: server_image
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "server_image:\n";
    to_block_style_yaml(msg.server_image, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const CameraImage_Response & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::CameraImage_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::CameraImage_Response & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::CameraImage_Response>()
{
  return "data_interface::srv::CameraImage_Response";
}

template<>
inline const char * name<data_interface::srv::CameraImage_Response>()
{
  return "data_interface/srv/CameraImage_Response";
}

template<>
struct has_fixed_size<data_interface::srv::CameraImage_Response>
  : std::integral_constant<bool, has_fixed_size<sensor_msgs::msg::Image>::value> {};

template<>
struct has_bounded_size<data_interface::srv::CameraImage_Response>
  : std::integral_constant<bool, has_bounded_size<sensor_msgs::msg::Image>::value> {};

template<>
struct is_message<data_interface::srv::CameraImage_Response>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace rosidl_generator_traits
{

template<>
inline const char * data_type<data_interface::srv::CameraImage>()
{
  return "data_interface::srv::CameraImage";
}

template<>
inline const char * name<data_interface::srv::CameraImage>()
{
  return "data_interface/srv/CameraImage";
}

template<>
struct has_fixed_size<data_interface::srv::CameraImage>
  : std::integral_constant<
    bool,
    has_fixed_size<data_interface::srv::CameraImage_Request>::value &&
    has_fixed_size<data_interface::srv::CameraImage_Response>::value
  >
{
};

template<>
struct has_bounded_size<data_interface::srv::CameraImage>
  : std::integral_constant<
    bool,
    has_bounded_size<data_interface::srv::CameraImage_Request>::value &&
    has_bounded_size<data_interface::srv::CameraImage_Response>::value
  >
{
};

template<>
struct is_service<data_interface::srv::CameraImage>
  : std::true_type
{
};

template<>
struct is_service_request<data_interface::srv::CameraImage_Request>
  : std::true_type
{
};

template<>
struct is_service_response<data_interface::srv::CameraImage_Response>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits

#endif  // DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__TRAITS_HPP_
