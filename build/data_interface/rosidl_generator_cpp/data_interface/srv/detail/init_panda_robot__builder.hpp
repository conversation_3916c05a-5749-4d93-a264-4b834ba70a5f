// generated from rosidl_generator_cpp/resource/idl__builder.hpp.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__BUILDER_HPP_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__BUILDER_HPP_

#include <algorithm>
#include <utility>

#include "data_interface/srv/detail/init_panda_robot__struct.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_InitPandaRobot_Request_request_string
{
public:
  Init_InitPandaRobot_Request_request_string()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::InitPandaRobot_Request request_string(::data_interface::srv::InitPandaRobot_Request::_request_string_type arg)
  {
    msg_.request_string = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::InitPandaRobot_Request msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::InitPandaRobot_Request>()
{
  return data_interface::srv::builder::Init_InitPandaRobot_Request_request_string();
}

}  // namespace data_interface


namespace data_interface
{

namespace srv
{

namespace builder
{

class Init_InitPandaRobot_Response_response_string
{
public:
  Init_InitPandaRobot_Response_response_string()
  : msg_(::rosidl_runtime_cpp::MessageInitialization::SKIP)
  {}
  ::data_interface::srv::InitPandaRobot_Response response_string(::data_interface::srv::InitPandaRobot_Response::_response_string_type arg)
  {
    msg_.response_string = std::move(arg);
    return std::move(msg_);
  }

private:
  ::data_interface::srv::InitPandaRobot_Response msg_;
};

}  // namespace builder

}  // namespace srv

template<typename MessageType>
auto build();

template<>
inline
auto build<::data_interface::srv::InitPandaRobot_Response>()
{
  return data_interface::srv::builder::Init_InitPandaRobot_Response_response_string();
}

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__BUILDER_HPP_
