// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_HPP_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


#ifndef _WIN32
# define DEPRECATED__data_interface__srv__InitPandaRobot_Request __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__InitPandaRobot_Request __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct InitPandaRobot_Request_
{
  using Type = InitPandaRobot_Request_<ContainerAllocator>;

  explicit InitPandaRobot_Request_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->request_string = "";
    }
  }

  explicit InitPandaRobot_Request_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : request_string(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->request_string = "";
    }
  }

  // field types and members
  using _request_string_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _request_string_type request_string;

  // setters for named parameter idiom
  Type & set__request_string(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->request_string = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__InitPandaRobot_Request
    std::shared_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__InitPandaRobot_Request
    std::shared_ptr<data_interface::srv::InitPandaRobot_Request_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const InitPandaRobot_Request_ & other) const
  {
    if (this->request_string != other.request_string) {
      return false;
    }
    return true;
  }
  bool operator!=(const InitPandaRobot_Request_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct InitPandaRobot_Request_

// alias to use template instance with default allocator
using InitPandaRobot_Request =
  data_interface::srv::InitPandaRobot_Request_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface


#ifndef _WIN32
# define DEPRECATED__data_interface__srv__InitPandaRobot_Response __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__InitPandaRobot_Response __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct InitPandaRobot_Response_
{
  using Type = InitPandaRobot_Response_<ContainerAllocator>;

  explicit InitPandaRobot_Response_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->response_string = "";
    }
  }

  explicit InitPandaRobot_Response_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : response_string(_alloc)
  {
    if (rosidl_runtime_cpp::MessageInitialization::ALL == _init ||
      rosidl_runtime_cpp::MessageInitialization::ZERO == _init)
    {
      this->response_string = "";
    }
  }

  // field types and members
  using _response_string_type =
    std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>>;
  _response_string_type response_string;

  // setters for named parameter idiom
  Type & set__response_string(
    const std::basic_string<char, std::char_traits<char>, typename std::allocator_traits<ContainerAllocator>::template rebind_alloc<char>> & _arg)
  {
    this->response_string = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__InitPandaRobot_Response
    std::shared_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__InitPandaRobot_Response
    std::shared_ptr<data_interface::srv::InitPandaRobot_Response_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const InitPandaRobot_Response_ & other) const
  {
    if (this->response_string != other.response_string) {
      return false;
    }
    return true;
  }
  bool operator!=(const InitPandaRobot_Response_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct InitPandaRobot_Response_

// alias to use template instance with default allocator
using InitPandaRobot_Response =
  data_interface::srv::InitPandaRobot_Response_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface

namespace data_interface
{

namespace srv
{

struct InitPandaRobot
{
  using Request = data_interface::srv::InitPandaRobot_Request;
  using Response = data_interface::srv::InitPandaRobot_Response;
};

}  // namespace srv

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_HPP_
