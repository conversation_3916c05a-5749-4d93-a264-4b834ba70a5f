// generated from rosidl_generator_cpp/resource/idl__struct.hpp.em
// with input from data_interface:srv/PoseEst3d2dSrv.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_HPP_
#define DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_HPP_

#include <algorithm>
#include <array>
#include <memory>
#include <string>
#include <vector>

#include "rosidl_runtime_cpp/bounded_vector.hpp"
#include "rosidl_runtime_cpp/message_initialization.hpp"


// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_points_msg__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Request __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Request __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct PoseEst3d2dSrv_Request_
{
  using Type = PoseEst3d2dSrv_Request_<ContainerAllocator>;

  explicit PoseEst3d2dSrv_Request_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : points(_init)
  {
    (void)_init;
  }

  explicit PoseEst3d2dSrv_Request_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : points(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _points_type =
    data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator>;
  _points_type points;

  // setters for named parameter idiom
  Type & set__points(
    const data_interface::msg::PoseEst3d2dPointsMsg_<ContainerAllocator> & _arg)
  {
    this->points = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Request
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Request
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Request_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PoseEst3d2dSrv_Request_ & other) const
  {
    if (this->points != other.points) {
      return false;
    }
    return true;
  }
  bool operator!=(const PoseEst3d2dSrv_Request_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PoseEst3d2dSrv_Request_

// alias to use template instance with default allocator
using PoseEst3d2dSrv_Request =
  data_interface::srv::PoseEst3d2dSrv_Request_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface


// Include directives for member types
// Member 'pose'
#include "geometry_msgs/msg/detail/pose_stamped__struct.hpp"

#ifndef _WIN32
# define DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Response __attribute__((deprecated))
#else
# define DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Response __declspec(deprecated)
#endif

namespace data_interface
{

namespace srv
{

// message struct
template<class ContainerAllocator>
struct PoseEst3d2dSrv_Response_
{
  using Type = PoseEst3d2dSrv_Response_<ContainerAllocator>;

  explicit PoseEst3d2dSrv_Response_(rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : pose(_init)
  {
    (void)_init;
  }

  explicit PoseEst3d2dSrv_Response_(const ContainerAllocator & _alloc, rosidl_runtime_cpp::MessageInitialization _init = rosidl_runtime_cpp::MessageInitialization::ALL)
  : pose(_alloc, _init)
  {
    (void)_init;
  }

  // field types and members
  using _pose_type =
    geometry_msgs::msg::PoseStamped_<ContainerAllocator>;
  _pose_type pose;

  // setters for named parameter idiom
  Type & set__pose(
    const geometry_msgs::msg::PoseStamped_<ContainerAllocator> & _arg)
  {
    this->pose = _arg;
    return *this;
  }

  // constant declarations

  // pointer types
  using RawPtr =
    data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> *;
  using ConstRawPtr =
    const data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> *;
  using SharedPtr =
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>>;
  using ConstSharedPtr =
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> const>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>>>
  using UniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>, Deleter>;

  using UniquePtr = UniquePtrWithDeleter<>;

  template<typename Deleter = std::default_delete<
      data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>>>
  using ConstUniquePtrWithDeleter =
    std::unique_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> const, Deleter>;
  using ConstUniquePtr = ConstUniquePtrWithDeleter<>;

  using WeakPtr =
    std::weak_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>>;
  using ConstWeakPtr =
    std::weak_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> const>;

  // pointer types similar to ROS 1, use SharedPtr / ConstSharedPtr instead
  // NOTE: Can't use 'using' here because GNU C++ can't parse attributes properly
  typedef DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Response
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator>>
    Ptr;
  typedef DEPRECATED__data_interface__srv__PoseEst3d2dSrv_Response
    std::shared_ptr<data_interface::srv::PoseEst3d2dSrv_Response_<ContainerAllocator> const>
    ConstPtr;

  // comparison operators
  bool operator==(const PoseEst3d2dSrv_Response_ & other) const
  {
    if (this->pose != other.pose) {
      return false;
    }
    return true;
  }
  bool operator!=(const PoseEst3d2dSrv_Response_ & other) const
  {
    return !this->operator==(other);
  }
};  // struct PoseEst3d2dSrv_Response_

// alias to use template instance with default allocator
using PoseEst3d2dSrv_Response =
  data_interface::srv::PoseEst3d2dSrv_Response_<std::allocator<void>>;

// constant definitions

}  // namespace srv

}  // namespace data_interface

namespace data_interface
{

namespace srv
{

struct PoseEst3d2dSrv
{
  using Request = data_interface::srv::PoseEst3d2dSrv_Request;
  using Response = data_interface::srv::PoseEst3d2dSrv_Response;
};

}  // namespace srv

}  // namespace data_interface

#endif  // DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__STRUCT_HPP_
