// generated from rosidl_generator_cpp/resource/idl__traits.hpp.em
// with input from data_interface:srv/PoseEst3d2dSrv.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__TRAITS_HPP_
#define DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__TRAITS_HPP_

#include <stdint.h>

#include <sstream>
#include <string>
#include <type_traits>

#include "data_interface/srv/detail/pose_est3d2d_srv__struct.hpp"
#include "rosidl_runtime_cpp/traits.hpp"

// Include directives for member types
// Member 'points'
#include "data_interface/msg/detail/pose_est3d2d_points_msg__traits.hpp"

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const PoseEst3d2dSrv_Request & msg,
  std::ostream & out)
{
  out << "{";
  // member: points
  {
    out << "points: ";
    to_flow_style_yaml(msg.points, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PoseEst3d2dSrv_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: points
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "points:\n";
    to_block_style_yaml(msg.points, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PoseEst3d2dSrv_Request & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::PoseEst3d2dSrv_Request & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::PoseEst3d2dSrv_Request & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::PoseEst3d2dSrv_Request>()
{
  return "data_interface::srv::PoseEst3d2dSrv_Request";
}

template<>
inline const char * name<data_interface::srv::PoseEst3d2dSrv_Request>()
{
  return "data_interface/srv/PoseEst3d2dSrv_Request";
}

template<>
struct has_fixed_size<data_interface::srv::PoseEst3d2dSrv_Request>
  : std::integral_constant<bool, has_fixed_size<data_interface::msg::PoseEst3d2dPointsMsg>::value> {};

template<>
struct has_bounded_size<data_interface::srv::PoseEst3d2dSrv_Request>
  : std::integral_constant<bool, has_bounded_size<data_interface::msg::PoseEst3d2dPointsMsg>::value> {};

template<>
struct is_message<data_interface::srv::PoseEst3d2dSrv_Request>
  : std::true_type {};

}  // namespace rosidl_generator_traits

// Include directives for member types
// Member 'pose'
#include "geometry_msgs/msg/detail/pose_stamped__traits.hpp"

namespace data_interface
{

namespace srv
{

inline void to_flow_style_yaml(
  const PoseEst3d2dSrv_Response & msg,
  std::ostream & out)
{
  out << "{";
  // member: pose
  {
    out << "pose: ";
    to_flow_style_yaml(msg.pose, out);
  }
  out << "}";
}  // NOLINT(readability/fn_size)

inline void to_block_style_yaml(
  const PoseEst3d2dSrv_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  // member: pose
  {
    if (indentation > 0) {
      out << std::string(indentation, ' ');
    }
    out << "pose:\n";
    to_block_style_yaml(msg.pose, out, indentation + 2);
  }
}  // NOLINT(readability/fn_size)

inline std::string to_yaml(const PoseEst3d2dSrv_Response & msg, bool use_flow_style = false)
{
  std::ostringstream out;
  if (use_flow_style) {
    to_flow_style_yaml(msg, out);
  } else {
    to_block_style_yaml(msg, out);
  }
  return out.str();
}

}  // namespace srv

}  // namespace data_interface

namespace rosidl_generator_traits
{

[[deprecated("use data_interface::srv::to_block_style_yaml() instead")]]
inline void to_yaml(
  const data_interface::srv::PoseEst3d2dSrv_Response & msg,
  std::ostream & out, size_t indentation = 0)
{
  data_interface::srv::to_block_style_yaml(msg, out, indentation);
}

[[deprecated("use data_interface::srv::to_yaml() instead")]]
inline std::string to_yaml(const data_interface::srv::PoseEst3d2dSrv_Response & msg)
{
  return data_interface::srv::to_yaml(msg);
}

template<>
inline const char * data_type<data_interface::srv::PoseEst3d2dSrv_Response>()
{
  return "data_interface::srv::PoseEst3d2dSrv_Response";
}

template<>
inline const char * name<data_interface::srv::PoseEst3d2dSrv_Response>()
{
  return "data_interface/srv/PoseEst3d2dSrv_Response";
}

template<>
struct has_fixed_size<data_interface::srv::PoseEst3d2dSrv_Response>
  : std::integral_constant<bool, has_fixed_size<geometry_msgs::msg::PoseStamped>::value> {};

template<>
struct has_bounded_size<data_interface::srv::PoseEst3d2dSrv_Response>
  : std::integral_constant<bool, has_bounded_size<geometry_msgs::msg::PoseStamped>::value> {};

template<>
struct is_message<data_interface::srv::PoseEst3d2dSrv_Response>
  : std::true_type {};

}  // namespace rosidl_generator_traits

namespace rosidl_generator_traits
{

template<>
inline const char * data_type<data_interface::srv::PoseEst3d2dSrv>()
{
  return "data_interface::srv::PoseEst3d2dSrv";
}

template<>
inline const char * name<data_interface::srv::PoseEst3d2dSrv>()
{
  return "data_interface/srv/PoseEst3d2dSrv";
}

template<>
struct has_fixed_size<data_interface::srv::PoseEst3d2dSrv>
  : std::integral_constant<
    bool,
    has_fixed_size<data_interface::srv::PoseEst3d2dSrv_Request>::value &&
    has_fixed_size<data_interface::srv::PoseEst3d2dSrv_Response>::value
  >
{
};

template<>
struct has_bounded_size<data_interface::srv::PoseEst3d2dSrv>
  : std::integral_constant<
    bool,
    has_bounded_size<data_interface::srv::PoseEst3d2dSrv_Request>::value &&
    has_bounded_size<data_interface::srv::PoseEst3d2dSrv_Response>::value
  >
{
};

template<>
struct is_service<data_interface::srv::PoseEst3d2dSrv>
  : std::true_type
{
};

template<>
struct is_service_request<data_interface::srv::PoseEst3d2dSrv_Request>
  : std::true_type
{
};

template<>
struct is_service_response<data_interface::srv::PoseEst3d2dSrv_Response>
  : std::true_type
{
};

}  // namespace rosidl_generator_traits

#endif  // DATA_INTERFACE__SRV__DETAIL__POSE_EST3D2D_SRV__TRAITS_HPP_
