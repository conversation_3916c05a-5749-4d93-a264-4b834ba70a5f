// generated from rosidl_typesupport_cpp/resource/idl__type_support.cpp.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#include "cstddef"
#include "rosidl_runtime_c/message_type_support_struct.h"
#include "data_interface/srv/detail/init_panda_robot__struct.hpp"
#include "rosidl_typesupport_cpp/identifier.hpp"
#include "rosidl_typesupport_cpp/message_type_support.hpp"
#include "rosidl_typesupport_c/type_support_map.h"
#include "rosidl_typesupport_cpp/message_type_support_dispatch.hpp"
#include "rosidl_typesupport_cpp/visibility_control.h"
#include "rosidl_typesupport_interface/macros.h"

namespace data_interface
{

namespace srv
{

namespace rosidl_typesupport_cpp
{

typedef struct _InitPandaRobot_Request_type_support_ids_t
{
  const char * typesupport_identifier[2];
} _InitPandaRobot_Request_type_support_ids_t;

static const _InitPandaRobot_Request_type_support_ids_t _InitPandaRobot_Request_message_typesupport_ids = {
  {
    "rosidl_typesupport_fastrtps_cpp",  // ::rosidl_typesupport_fastrtps_cpp::typesupport_identifier,
    "rosidl_typesupport_introspection_cpp",  // ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  }
};

typedef struct _InitPandaRobot_Request_type_support_symbol_names_t
{
  const char * symbol_name[2];
} _InitPandaRobot_Request_type_support_symbol_names_t;

#define STRINGIFY_(s) #s
#define STRINGIFY(s) STRINGIFY_(s)

static const _InitPandaRobot_Request_type_support_symbol_names_t _InitPandaRobot_Request_message_typesupport_symbol_names = {
  {
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_cpp, data_interface, srv, InitPandaRobot_Request)),
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, data_interface, srv, InitPandaRobot_Request)),
  }
};

typedef struct _InitPandaRobot_Request_type_support_data_t
{
  void * data[2];
} _InitPandaRobot_Request_type_support_data_t;

static _InitPandaRobot_Request_type_support_data_t _InitPandaRobot_Request_message_typesupport_data = {
  {
    0,  // will store the shared library later
    0,  // will store the shared library later
  }
};

static const type_support_map_t _InitPandaRobot_Request_message_typesupport_map = {
  2,
  "data_interface",
  &_InitPandaRobot_Request_message_typesupport_ids.typesupport_identifier[0],
  &_InitPandaRobot_Request_message_typesupport_symbol_names.symbol_name[0],
  &_InitPandaRobot_Request_message_typesupport_data.data[0],
};

static const rosidl_message_type_support_t InitPandaRobot_Request_message_type_support_handle = {
  ::rosidl_typesupport_cpp::typesupport_identifier,
  reinterpret_cast<const type_support_map_t *>(&_InitPandaRobot_Request_message_typesupport_map),
  ::rosidl_typesupport_cpp::get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_cpp

}  // namespace srv

}  // namespace data_interface

namespace rosidl_typesupport_cpp
{

template<>
ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<data_interface::srv::InitPandaRobot_Request>()
{
  return &::data_interface::srv::rosidl_typesupport_cpp::InitPandaRobot_Request_message_type_support_handle;
}

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_cpp, data_interface, srv, InitPandaRobot_Request)() {
  return get_message_type_support_handle<data_interface::srv::InitPandaRobot_Request>();
}

#ifdef __cplusplus
}
#endif
}  // namespace rosidl_typesupport_cpp

// already included above
// #include "cstddef"
// already included above
// #include "rosidl_runtime_c/message_type_support_struct.h"
// already included above
// #include "data_interface/srv/detail/init_panda_robot__struct.hpp"
// already included above
// #include "rosidl_typesupport_cpp/identifier.hpp"
// already included above
// #include "rosidl_typesupport_cpp/message_type_support.hpp"
// already included above
// #include "rosidl_typesupport_c/type_support_map.h"
// already included above
// #include "rosidl_typesupport_cpp/message_type_support_dispatch.hpp"
// already included above
// #include "rosidl_typesupport_cpp/visibility_control.h"
// already included above
// #include "rosidl_typesupport_interface/macros.h"

namespace data_interface
{

namespace srv
{

namespace rosidl_typesupport_cpp
{

typedef struct _InitPandaRobot_Response_type_support_ids_t
{
  const char * typesupport_identifier[2];
} _InitPandaRobot_Response_type_support_ids_t;

static const _InitPandaRobot_Response_type_support_ids_t _InitPandaRobot_Response_message_typesupport_ids = {
  {
    "rosidl_typesupport_fastrtps_cpp",  // ::rosidl_typesupport_fastrtps_cpp::typesupport_identifier,
    "rosidl_typesupport_introspection_cpp",  // ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  }
};

typedef struct _InitPandaRobot_Response_type_support_symbol_names_t
{
  const char * symbol_name[2];
} _InitPandaRobot_Response_type_support_symbol_names_t;

#define STRINGIFY_(s) #s
#define STRINGIFY(s) STRINGIFY_(s)

static const _InitPandaRobot_Response_type_support_symbol_names_t _InitPandaRobot_Response_message_typesupport_symbol_names = {
  {
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_fastrtps_cpp, data_interface, srv, InitPandaRobot_Response)),
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, data_interface, srv, InitPandaRobot_Response)),
  }
};

typedef struct _InitPandaRobot_Response_type_support_data_t
{
  void * data[2];
} _InitPandaRobot_Response_type_support_data_t;

static _InitPandaRobot_Response_type_support_data_t _InitPandaRobot_Response_message_typesupport_data = {
  {
    0,  // will store the shared library later
    0,  // will store the shared library later
  }
};

static const type_support_map_t _InitPandaRobot_Response_message_typesupport_map = {
  2,
  "data_interface",
  &_InitPandaRobot_Response_message_typesupport_ids.typesupport_identifier[0],
  &_InitPandaRobot_Response_message_typesupport_symbol_names.symbol_name[0],
  &_InitPandaRobot_Response_message_typesupport_data.data[0],
};

static const rosidl_message_type_support_t InitPandaRobot_Response_message_type_support_handle = {
  ::rosidl_typesupport_cpp::typesupport_identifier,
  reinterpret_cast<const type_support_map_t *>(&_InitPandaRobot_Response_message_typesupport_map),
  ::rosidl_typesupport_cpp::get_message_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_cpp

}  // namespace srv

}  // namespace data_interface

namespace rosidl_typesupport_cpp
{

template<>
ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_message_type_support_t *
get_message_type_support_handle<data_interface::srv::InitPandaRobot_Response>()
{
  return &::data_interface::srv::rosidl_typesupport_cpp::InitPandaRobot_Response_message_type_support_handle;
}

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_cpp, data_interface, srv, InitPandaRobot_Response)() {
  return get_message_type_support_handle<data_interface::srv::InitPandaRobot_Response>();
}

#ifdef __cplusplus
}
#endif
}  // namespace rosidl_typesupport_cpp

// already included above
// #include "cstddef"
#include "rosidl_runtime_c/service_type_support_struct.h"
// already included above
// #include "data_interface/srv/detail/init_panda_robot__struct.hpp"
// already included above
// #include "rosidl_typesupport_cpp/identifier.hpp"
#include "rosidl_typesupport_cpp/service_type_support.hpp"
// already included above
// #include "rosidl_typesupport_c/type_support_map.h"
#include "rosidl_typesupport_cpp/service_type_support_dispatch.hpp"
// already included above
// #include "rosidl_typesupport_cpp/visibility_control.h"
// already included above
// #include "rosidl_typesupport_interface/macros.h"

namespace data_interface
{

namespace srv
{

namespace rosidl_typesupport_cpp
{

typedef struct _InitPandaRobot_type_support_ids_t
{
  const char * typesupport_identifier[2];
} _InitPandaRobot_type_support_ids_t;

static const _InitPandaRobot_type_support_ids_t _InitPandaRobot_service_typesupport_ids = {
  {
    "rosidl_typesupport_fastrtps_cpp",  // ::rosidl_typesupport_fastrtps_cpp::typesupport_identifier,
    "rosidl_typesupport_introspection_cpp",  // ::rosidl_typesupport_introspection_cpp::typesupport_identifier,
  }
};

typedef struct _InitPandaRobot_type_support_symbol_names_t
{
  const char * symbol_name[2];
} _InitPandaRobot_type_support_symbol_names_t;

#define STRINGIFY_(s) #s
#define STRINGIFY(s) STRINGIFY_(s)

static const _InitPandaRobot_type_support_symbol_names_t _InitPandaRobot_service_typesupport_symbol_names = {
  {
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(rosidl_typesupport_fastrtps_cpp, data_interface, srv, InitPandaRobot)),
    STRINGIFY(ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(rosidl_typesupport_introspection_cpp, data_interface, srv, InitPandaRobot)),
  }
};

typedef struct _InitPandaRobot_type_support_data_t
{
  void * data[2];
} _InitPandaRobot_type_support_data_t;

static _InitPandaRobot_type_support_data_t _InitPandaRobot_service_typesupport_data = {
  {
    0,  // will store the shared library later
    0,  // will store the shared library later
  }
};

static const type_support_map_t _InitPandaRobot_service_typesupport_map = {
  2,
  "data_interface",
  &_InitPandaRobot_service_typesupport_ids.typesupport_identifier[0],
  &_InitPandaRobot_service_typesupport_symbol_names.symbol_name[0],
  &_InitPandaRobot_service_typesupport_data.data[0],
};

static const rosidl_service_type_support_t InitPandaRobot_service_type_support_handle = {
  ::rosidl_typesupport_cpp::typesupport_identifier,
  reinterpret_cast<const type_support_map_t *>(&_InitPandaRobot_service_typesupport_map),
  ::rosidl_typesupport_cpp::get_service_typesupport_handle_function,
};

}  // namespace rosidl_typesupport_cpp

}  // namespace srv

}  // namespace data_interface

namespace rosidl_typesupport_cpp
{

template<>
ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_service_type_support_t *
get_service_type_support_handle<data_interface::srv::InitPandaRobot>()
{
  return &::data_interface::srv::rosidl_typesupport_cpp::InitPandaRobot_service_type_support_handle;
}

}  // namespace rosidl_typesupport_cpp

#ifdef __cplusplus
extern "C"
{
#endif

ROSIDL_TYPESUPPORT_CPP_PUBLIC
const rosidl_service_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(rosidl_typesupport_cpp, data_interface, srv, InitPandaRobot)() {
  return ::rosidl_typesupport_cpp::get_service_type_support_handle<data_interface::srv::InitPandaRobot>();
}

#ifdef __cplusplus
}
#endif
