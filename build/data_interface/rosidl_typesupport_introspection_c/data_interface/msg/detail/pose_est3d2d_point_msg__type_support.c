// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "data_interface/msg/detail/pose_est3d2d_point_msg__rosidl_typesupport_introspection_c.h"
#include "data_interface/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "data_interface/msg/detail/pose_est3d2d_point_msg__functions.h"
#include "data_interface/msg/detail/pose_est3d2d_point_msg__struct.h"


#ifdef __cplusplus
extern "C"
{
#endif

void data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  data_interface__msg__PoseEst3d2dPointMsg__init(message_memory);
}

void data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_fini_function(void * message_memory)
{
  data_interface__msg__PoseEst3d2dPointMsg__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_member_array[3] = {
  {
    "x",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface__msg__PoseEst3d2dPointMsg, x),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "y",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_DOUBLE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface__msg__PoseEst3d2dPointMsg, y),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  },
  {
    "label",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_INT8,  // type
    0,  // upper bound of string
    NULL,  // members of sub message
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface__msg__PoseEst3d2dPointMsg, label),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_members = {
  "data_interface__msg",  // message namespace
  "PoseEst3d2dPointMsg",  // message name
  3,  // number of fields
  sizeof(data_interface__msg__PoseEst3d2dPointMsg),
  data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_member_array,  // message members
  data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_init_function,  // function to initialize message memory (memory has to be allocated)
  data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_type_support_handle = {
  0,
  &data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, msg, PoseEst3d2dPointMsg)() {
  if (!data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_type_support_handle.typesupport_identifier) {
    data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &data_interface__msg__PoseEst3d2dPointMsg__rosidl_typesupport_introspection_c__PoseEst3d2dPointMsg_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif
