// generated from rosidl_typesupport_introspection_c/resource/idl__type_support.c.em
// with input from data_interface:srv/PoseEst3d2dSrv.idl
// generated code does not contain a copyright notice

#include <stddef.h>
#include "data_interface/srv/detail/pose_est3d2d_srv__rosidl_typesupport_introspection_c.h"
#include "data_interface/msg/rosidl_typesupport_introspection_c__visibility_control.h"
#include "rosidl_typesupport_introspection_c/field_types.h"
#include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/message_introspection.h"
#include "data_interface/srv/detail/pose_est3d2d_srv__functions.h"
#include "data_interface/srv/detail/pose_est3d2d_srv__struct.h"


// Include directives for member types
// Member `points`
#include "data_interface/msg/pose_est3d2d_points_msg.h"
// Member `points`
#include "data_interface/msg/detail/pose_est3d2d_points_msg__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  data_interface__srv__PoseEst3d2dSrv_Request__init(message_memory);
}

void data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_fini_function(void * message_memory)
{
  data_interface__srv__PoseEst3d2dSrv_Request__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_member_array[1] = {
  {
    "points",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface__srv__PoseEst3d2dSrv_Request, points),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_members = {
  "data_interface__srv",  // message namespace
  "PoseEst3d2dSrv_Request",  // message name
  1,  // number of fields
  sizeof(data_interface__srv__PoseEst3d2dSrv_Request),
  data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_member_array,  // message members
  data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_init_function,  // function to initialize message memory (memory has to be allocated)
  data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_type_support_handle = {
  0,
  &data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Request)() {
  data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, msg, PoseEst3d2dPointsMsg)();
  if (!data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_type_support_handle.typesupport_identifier) {
    data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &data_interface__srv__PoseEst3d2dSrv_Request__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif

// already included above
// #include <stddef.h>
// already included above
// #include "data_interface/srv/detail/pose_est3d2d_srv__rosidl_typesupport_introspection_c.h"
// already included above
// #include "data_interface/msg/rosidl_typesupport_introspection_c__visibility_control.h"
// already included above
// #include "rosidl_typesupport_introspection_c/field_types.h"
// already included above
// #include "rosidl_typesupport_introspection_c/identifier.h"
// already included above
// #include "rosidl_typesupport_introspection_c/message_introspection.h"
// already included above
// #include "data_interface/srv/detail/pose_est3d2d_srv__functions.h"
// already included above
// #include "data_interface/srv/detail/pose_est3d2d_srv__struct.h"


// Include directives for member types
// Member `pose`
#include "geometry_msgs/msg/pose_stamped.h"
// Member `pose`
#include "geometry_msgs/msg/detail/pose_stamped__rosidl_typesupport_introspection_c.h"

#ifdef __cplusplus
extern "C"
{
#endif

void data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_init_function(
  void * message_memory, enum rosidl_runtime_c__message_initialization _init)
{
  // TODO(karsten1987): initializers are not yet implemented for typesupport c
  // see https://github.com/ros2/ros2/issues/397
  (void) _init;
  data_interface__srv__PoseEst3d2dSrv_Response__init(message_memory);
}

void data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_fini_function(void * message_memory)
{
  data_interface__srv__PoseEst3d2dSrv_Response__fini(message_memory);
}

static rosidl_typesupport_introspection_c__MessageMember data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_member_array[1] = {
  {
    "pose",  // name
    rosidl_typesupport_introspection_c__ROS_TYPE_MESSAGE,  // type
    0,  // upper bound of string
    NULL,  // members of sub message (initialized later)
    false,  // is array
    0,  // array size
    false,  // is upper bound
    offsetof(data_interface__srv__PoseEst3d2dSrv_Response, pose),  // bytes offset in struct
    NULL,  // default value
    NULL,  // size() function pointer
    NULL,  // get_const(index) function pointer
    NULL,  // get(index) function pointer
    NULL,  // fetch(index, &value) function pointer
    NULL,  // assign(index, value) function pointer
    NULL  // resize(index) function pointer
  }
};

static const rosidl_typesupport_introspection_c__MessageMembers data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_members = {
  "data_interface__srv",  // message namespace
  "PoseEst3d2dSrv_Response",  // message name
  1,  // number of fields
  sizeof(data_interface__srv__PoseEst3d2dSrv_Response),
  data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_member_array,  // message members
  data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_init_function,  // function to initialize message memory (memory has to be allocated)
  data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_fini_function  // function to terminate message instance (will not free memory)
};

// this is not const since it must be initialized on first access
// since C does not allow non-integral compile-time constants
static rosidl_message_type_support_t data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_type_support_handle = {
  0,
  &data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_members,
  get_message_typesupport_handle_function,
};

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Response)() {
  data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_member_array[0].members_ =
    ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, geometry_msgs, msg, PoseStamped)();
  if (!data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_type_support_handle.typesupport_identifier) {
    data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  return &data_interface__srv__PoseEst3d2dSrv_Response__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_type_support_handle;
}
#ifdef __cplusplus
}
#endif

#include "rosidl_runtime_c/service_type_support_struct.h"
// already included above
// #include "data_interface/msg/rosidl_typesupport_introspection_c__visibility_control.h"
// already included above
// #include "data_interface/srv/detail/pose_est3d2d_srv__rosidl_typesupport_introspection_c.h"
// already included above
// #include "rosidl_typesupport_introspection_c/identifier.h"
#include "rosidl_typesupport_introspection_c/service_introspection.h"

// this is intentionally not const to allow initialization later to prevent an initialization race
static rosidl_typesupport_introspection_c__ServiceMembers data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_members = {
  "data_interface__srv",  // service namespace
  "PoseEst3d2dSrv",  // service name
  // these two fields are initialized below on the first access
  NULL,  // request message
  // data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Request_message_type_support_handle,
  NULL  // response message
  // data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_Response_message_type_support_handle
};

static rosidl_service_type_support_t data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_type_support_handle = {
  0,
  &data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_members,
  get_service_typesupport_handle_function,
};

// Forward declaration of request/response type support functions
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Request)();

const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Response)();

ROSIDL_TYPESUPPORT_INTROSPECTION_C_EXPORT_data_interface
const rosidl_service_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv)() {
  if (!data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_type_support_handle.typesupport_identifier) {
    data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_type_support_handle.typesupport_identifier =
      rosidl_typesupport_introspection_c__identifier;
  }
  rosidl_typesupport_introspection_c__ServiceMembers * service_members =
    (rosidl_typesupport_introspection_c__ServiceMembers *)data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_type_support_handle.data;

  if (!service_members->request_members_) {
    service_members->request_members_ =
      (const rosidl_typesupport_introspection_c__MessageMembers *)
      ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Request)()->data;
  }
  if (!service_members->response_members_) {
    service_members->response_members_ =
      (const rosidl_typesupport_introspection_c__MessageMembers *)
      ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(rosidl_typesupport_introspection_c, data_interface, srv, PoseEst3d2dSrv_Response)()->data;
  }

  return &data_interface__srv__detail__pose_est3d2d_srv__rosidl_typesupport_introspection_c__PoseEst3d2dSrv_service_type_support_handle;
}
