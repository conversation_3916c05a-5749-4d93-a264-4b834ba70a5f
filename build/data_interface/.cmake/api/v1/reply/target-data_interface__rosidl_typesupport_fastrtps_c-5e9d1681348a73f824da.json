{"artifacts": [{"path": "libdata_interface__rosidl_typesupport_fastrtps_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "add_compile_options", "add_definitions", "find_package", "target_include_directories", "set_target_properties"], "files": ["/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 16, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 104, "parent": 4}, {"command": 4, "file": 0, "line": 167, "parent": 4}, {"command": 5, "file": 0, "line": 131, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 141, "parent": 4}, {"command": 5, "file": 0, "line": 115, "parent": 4}, {"command": 6, "file": 3, "line": 5, "parent": 0}, {"command": 8, "file": 0, "line": 21, "parent": 4}, {"file": 5, "parent": 14}, {"command": 1, "file": 5, "line": 41, "parent": 15}, {"file": 4, "parent": 16}, {"command": 7, "file": 4, "line": 25, "parent": 17}, {"command": 9, "file": 0, "line": 134, "parent": 4}, {"command": 10, "file": 0, "line": 110, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 13, "fragment": "-Wall"}, {"backtrace": 13, "fragment": "-Wextra"}, {"backtrace": 13, "fragment": "-Wpedantic"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 12, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_FASTRTPS_C_BUILDING_DLL_data_interface"}, {"backtrace": 18, "define": "ROS_PACKAGE_NAME=\"data_interface\""}], "includes": [{"backtrace": 19, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 12, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "CXX", "languageStandard": {"backtraces": [20], "standard": "14"}, "sourceIndexes": [1, 3, 5, 7, 9]}], "dependencies": [{"backtrace": 7, "id": "data_interface__rosidl_generator_c::@6890427a1f51a3e7e1df"}], "id": "data_interface__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/data_interface"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ccag/ccag_ws/build/data_interface:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "libdata_interface__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 12, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "CXX"}, "name": "data_interface__rosidl_typesupport_fastrtps_c", "nameOnDisk": "libdata_interface__rosidl_typesupport_fastrtps_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 2, 4, 6, 8]}, {"name": "Source Files", "sourceIndexes": [1, 3, 5, 7, 9]}, {"name": "CMake Rules", "sourceIndexes": [10]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/msg/detail/pose_est3d2d_point_msg__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/msg/detail/pose_est3d2d_point_msg__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/msg/detail/pose_est3d2d_points_msg__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/msg/detail/pose_est3d2d_points_msg__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/pose_est3d2d_srv__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/pose_est3d2d_srv__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/camera_image__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/camera_image__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/init_panda_robot__rosidl_typesupport_fastrtps_c.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/srv/detail/init_panda_robot__type_support_c.cpp", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_fastrtps_c/data_interface/msg/detail/pose_est3d2d_point_msg__rosidl_typesupport_fastrtps_c.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}