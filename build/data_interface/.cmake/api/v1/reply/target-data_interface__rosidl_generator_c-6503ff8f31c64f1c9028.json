{"artifacts": [{"path": "libdata_interface__rosidl_generator_c.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "set_target_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 16, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 110, "parent": 4}, {"command": 4, "file": 0, "line": 164, "parent": 4}, {"command": 5, "file": 0, "line": 132, "parent": 4}, {"command": 5, "file": 0, "line": 132, "parent": 4}, {"command": 5, "file": 0, "line": 132, "parent": 4}, {"command": 5, "file": 0, "line": 132, "parent": 4}, {"command": 5, "file": 0, "line": 137, "parent": 4}, {"command": 6, "file": 0, "line": 119, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 6, "parent": 13}, {"command": 8, "file": 6, "line": 21, "parent": 14}, {"file": 5, "parent": 15}, {"command": 1, "file": 5, "line": 41, "parent": 16}, {"file": 4, "parent": 17}, {"command": 7, "file": 4, "line": 25, "parent": 18}, {"command": 9, "file": 0, "line": 125, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 12, "fragment": "-Wall"}, {"fragment": "-std=gnu11"}], "defines": [{"backtrace": 7, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_GENERATOR_C_BUILDING_DLL_data_interface"}, {"backtrace": 19, "define": "ROS_PACKAGE_NAME=\"data_interface\""}], "includes": [{"backtrace": 20, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}], "language": "C", "languageStandard": {"backtraces": [12], "standard": "11"}, "sourceIndexes": [20, 21, 22, 23, 24]}], "id": "data_interface__rosidl_generator_c::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/data_interface"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}], "language": "C"}, "name": "data_interface__rosidl_generator_c", "nameOnDisk": "libdata_interface__rosidl_generator_c.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "<PERSON><PERSON>", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19]}, {"name": "Source Files", "sourceIndexes": [20, 21, 22, 23, 24]}, {"name": "CMake Rules", "sourceIndexes": [25]}], "sources": [{"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/pose_est3d2d_point_msg.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_point_msg__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_point_msg__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_point_msg__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/pose_est3d2d_points_msg.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_points_msg__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_points_msg__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_points_msg__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/pose_est3d2d_srv.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/pose_est3d2d_srv__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/pose_est3d2d_srv__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/pose_est3d2d_srv__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/camera_image.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/camera_image__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/camera_image__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/camera_image__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/init_panda_robot.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/init_panda_robot__functions.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/init_panda_robot__struct.h", "sourceGroupIndex": 0}, {"backtrace": 5, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/init_panda_robot__type_support.h", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_point_msg__functions.c", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/detail/pose_est3d2d_points_msg__functions.c", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/pose_est3d2d_srv__functions.c", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/camera_image__functions.c", "sourceGroupIndex": 1}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/srv/detail/init_panda_robot__functions.c", "sourceGroupIndex": 1}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c/data_interface/msg/pose_est3d2d_point_msg.h.rule", "sourceGroupIndex": 2}], "type": "SHARED_LIBRARY"}