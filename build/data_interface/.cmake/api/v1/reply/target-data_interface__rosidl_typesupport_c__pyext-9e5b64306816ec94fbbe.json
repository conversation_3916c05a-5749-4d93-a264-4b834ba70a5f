{"artifacts": [{"path": "rosidl_generator_py/data_interface/data_interface_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "ament_target_dependencies", "add_dependencies", "set_target_properties", "set_properties", "add_definitions", "find_package", "target_include_directories"], "files": ["/opt/ros/humble/share/rosidl_generator_py/cmake/rosidl_generator_py_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/ament_cmake_target_dependencies/cmake/ament_target_dependencies.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 16, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 226, "parent": 4}, {"command": 4, "file": 0, "line": 282, "parent": 4}, {"command": 5, "file": 0, "line": 246, "parent": 4}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 8}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 10}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 12}, {"command": 6, "file": 0, "line": 276, "parent": 4}, {"command": 5, "file": 4, "line": 151, "parent": 14}, {"command": 6, "file": 0, "line": 268, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 16}, {"command": 6, "file": 0, "line": 262, "parent": 4}, {"command": 5, "file": 4, "line": 145, "parent": 18}, {"command": 7, "file": 0, "line": 229, "parent": 4}, {"command": 9, "file": 0, "line": 239, "parent": 4}, {"command": 8, "file": 0, "line": 146, "parent": 21}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 7, "parent": 23}, {"command": 11, "file": 7, "line": 21, "parent": 24}, {"file": 6, "parent": 25}, {"command": 1, "file": 6, "line": 41, "parent": 26}, {"file": 5, "parent": 27}, {"command": 10, "file": 5, "line": 25, "parent": 28}, {"command": 12, "file": 0, "line": 253, "parent": 4}, {"command": 12, "file": 4, "line": 141, "parent": 18}, {"command": 12, "file": 4, "line": 141, "parent": 8}, {"command": 12, "file": 4, "line": 141, "parent": 16}, {"command": 12, "file": 4, "line": 141, "parent": 10}, {"command": 12, "file": 4, "line": 141, "parent": 12}, {"command": 12, "file": 4, "line": 147, "parent": 14}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 22, "fragment": "-Wall"}, {"backtrace": 22, "fragment": "-Wextra"}], "defines": [{"backtrace": 7, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"backtrace": 29, "define": "ROS_PACKAGE_NAME=\"data_interface\""}, {"define": "data_interface__rosidl_typesupport_c__pyext_EXPORTS"}], "includes": [{"backtrace": 30, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_c"}, {"backtrace": 30, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_py"}, {"backtrace": 30, "path": "/usr/include/python3.10"}, {"backtrace": 31, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 31, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}, {"backtrace": 31, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 32, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 33, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 34, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 35, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 36, "isSystem": true, "path": "/opt/ros/humble/include/rmw"}, {"backtrace": 7, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/fastcdr"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_cpp"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_fastrtps_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_c"}, {"backtrace": 9, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_introspection_cpp"}], "language": "C", "sourceIndexes": [0]}], "dependencies": [{"backtrace": 7, "id": "data_interface__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 20, "id": "data_interface__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 7, "id": "data_interface__rosidl_generator_py::@6890427a1f51a3e7e1df"}, {"backtrace": 20, "id": "data_interface__py::@255962fb3cb9f93bf632"}], "id": "data_interface__rosidl_typesupport_c__pyext::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "local/lib/python3.10/dist-packages/data_interface"}, {"backtrace": 6, "path": "local/lib/python3.10/dist-packages/data_interface"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/data_interface"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-rpath,/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_py/data_interface:/home/<USER>/ccag/ccag_ws/build/data_interface:/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "rosidl_generator_py/data_interface/libdata_interface__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 7, "fragment": "libdata_interface__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_py.so", "role": "libraries"}, {"backtrace": 7, "fragment": "/usr/lib/x86_64-linux-gnu/libpython3.10.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_c.so", "role": "libraries"}, {"fragment": "libdata_interface__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_fastrtps_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/libfastcdr.so.1.0.24", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librmw.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_generator_c.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_cpp.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librosidl_typesupport_introspection_c.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 13, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 17, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 19, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"backtrace": 15, "fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "C"}, "name": "data_interface__rosidl_typesupport_c__pyext", "nameOnDisk": "data_interface_s__rosidl_typesupport_c.cpython-310-x86_64-linux-gnu.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_py/data_interface/_data_interface_s.ep.rosidl_typesupport_c.c", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}