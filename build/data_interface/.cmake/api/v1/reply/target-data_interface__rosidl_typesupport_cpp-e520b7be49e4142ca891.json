{"artifacts": [{"path": "libdata_interface__rosidl_typesupport_cpp.so"}], "backtrace": 5, "backtraceGraph": {"commands": ["add_library", "include", "ament_execute_extensions", "rosidl_generate_interfaces", "install", "target_link_libraries", "add_dependencies", "add_compile_options", "add_definitions", "find_package", "set_target_properties"], "files": ["/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_ros-extras.cmake", "/opt/ros/humble/share/ament_cmake_ros/cmake/ament_cmake_rosConfig.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake"], "nodes": [{"file": 3}, {"command": 3, "file": 3, "line": 16, "parent": 0}, {"command": 2, "file": 2, "line": 286, "parent": 1}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 0, "parent": 3}, {"command": 0, "file": 0, "line": 87, "parent": 4}, {"command": 4, "file": 0, "line": 140, "parent": 4}, {"command": 5, "file": 0, "line": 129, "parent": 4}, {"command": 5, "file": 0, "line": 129, "parent": 4}, {"command": 5, "file": 0, "line": 129, "parent": 4}, {"command": 5, "file": 0, "line": 129, "parent": 4}, {"command": 5, "file": 0, "line": 121, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 4, "parent": 12}, {"command": 6, "file": 4, "line": 139, "parent": 13}, {"command": 7, "file": 3, "line": 5, "parent": 0}, {"command": 5, "file": 0, "line": 118, "parent": 4}, {"command": 1, "file": 1, "line": 48, "parent": 2}, {"file": 7, "parent": 17}, {"command": 9, "file": 7, "line": 21, "parent": 18}, {"file": 6, "parent": 19}, {"command": 1, "file": 6, "line": 41, "parent": 20}, {"file": 5, "parent": 21}, {"command": 8, "file": 5, "line": 25, "parent": 22}, {"command": 10, "file": 0, "line": 92, "parent": 4}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-fPIC"}, {"backtrace": 15, "fragment": "-Wall"}, {"backtrace": 15, "fragment": "-Wextra"}, {"backtrace": 15, "fragment": "-Wpedantic"}, {"fragment": "-std=gnu++14"}], "defines": [{"backtrace": 16, "define": "RCUTILS_ENABLE_FAULT_INJECTION"}, {"define": "ROSIDL_TYPESUPPORT_CPP_BUILDING_DLL"}, {"backtrace": 23, "define": "ROS_PACKAGE_NAME=\"data_interface\""}], "includes": [{"backtrace": 16, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_generator_cpp"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/sensor_msgs"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/builtin_interfaces"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_cpp"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_runtime_c"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rcutils"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_interface"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/geometry_msgs"}, {"backtrace": 16, "isSystem": true, "path": "/opt/ros/humble/include/std_msgs"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_cpp"}, {"backtrace": 11, "isSystem": true, "path": "/opt/ros/humble/include/rosidl_typesupport_c"}], "language": "CXX", "languageStandard": {"backtraces": [24], "standard": "14"}, "sourceIndexes": [0, 1, 2, 3, 4]}], "dependencies": [{"backtrace": 14, "id": "data_interface__cpp::@6890427a1f51a3e7e1df"}], "id": "data_interface__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 6, "path": "lib"}, {"backtrace": 6, "path": "lib"}], "prefix": {"path": "/home/<USER>/ccag/ccag_ws/install/data_interface"}}, "link": {"commandFragments": [{"fragment": "", "role": "flags"}, {"fragment": "-Wl,-r<PERSON>,/opt/ros/humble/lib:", "role": "libraries"}, {"backtrace": 7, "fragment": "/opt/ros/humble/lib/libsensor_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 8, "fragment": "/opt/ros/humble/lib/libgeometry_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 9, "fragment": "/opt/ros/humble/lib/libstd_msgs__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 10, "fragment": "/opt/ros/humble/lib/libbuiltin_interfaces__rosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_cpp.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_typesupport_c.so", "role": "libraries"}, {"backtrace": 11, "fragment": "/opt/ros/humble/lib/librosidl_runtime_c.so", "role": "libraries"}, {"fragment": "/opt/ros/humble/lib/librcutils.so", "role": "libraries"}, {"fragment": "-ldl", "role": "libraries"}, {"fragment": "-Wl,-rpath-link,/opt/ros/humble/lib", "role": "libraries"}], "language": "CXX"}, "name": "data_interface__rosidl_typesupport_cpp", "nameOnDisk": "libdata_interface__rosidl_typesupport_cpp.so", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4]}, {"name": "CMake Rules", "sourceIndexes": [5]}], "sources": [{"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/msg/pose_est3d2d_point_msg__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/msg/pose_est3d2d_points_msg__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/srv/pose_est3d2d_srv__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/srv/camera_image__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 5, "compileGroupIndex": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/srv/init_panda_robot__type_support.cpp", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_typesupport_cpp/data_interface/msg/pose_est3d2d_point_msg__type_support.cpp.rule", "sourceGroupIndex": 1}], "type": "SHARED_LIBRARY"}