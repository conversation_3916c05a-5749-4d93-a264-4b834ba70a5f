{"backtrace": 2, "backtraceGraph": {"commands": ["add_custom_target", "rosidl_generate_interfaces", "add_dependencies", "include", "ament_execute_extensions"], "files": ["/opt/ros/humble/share/rosidl_cmake/cmake/rosidl_generate_interfaces.cmake", "CMakeLists.txt", "/opt/ros/humble/share/rosidl_generator_c/cmake/rosidl_generator_c_generate_interfaces.cmake", "/opt/ros/humble/share/ament_cmake_core/cmake/core/ament_execute_extensions.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_c/cmake/rosidl_typesupport_fastrtps_c_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_generator_cpp/cmake/rosidl_generator_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_fastrtps_cpp/cmake/rosidl_typesupport_fastrtps_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_introspection_c/cmake/rosidl_typesupport_introspection_c_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_c/cmake/rosidl_typesupport_c_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_introspection_cpp/cmake/rosidl_typesupport_introspection_cpp_generate_interfaces.cmake", "/opt/ros/humble/share/rosidl_typesupport_cpp/cmake/rosidl_typesupport_cpp_generate_interfaces.cmake"], "nodes": [{"file": 1}, {"command": 1, "file": 1, "line": 16, "parent": 0}, {"command": 0, "file": 0, "line": 213, "parent": 1}, {"command": 4, "file": 0, "line": 286, "parent": 1}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 2, "parent": 4}, {"command": 2, "file": 2, "line": 143, "parent": 5}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 4, "parent": 7}, {"command": 2, "file": 4, "line": 145, "parent": 8}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 5, "parent": 10}, {"command": 2, "file": 5, "line": 139, "parent": 11}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 6, "parent": 13}, {"command": 2, "file": 6, "line": 163, "parent": 14}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 7, "parent": 16}, {"command": 2, "file": 7, "line": 135, "parent": 17}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 8, "parent": 19}, {"command": 2, "file": 8, "line": 135, "parent": 20}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 9, "parent": 22}, {"command": 2, "file": 9, "line": 134, "parent": 23}, {"command": 3, "file": 3, "line": 48, "parent": 3}, {"file": 10, "parent": 25}, {"command": 2, "file": 10, "line": 134, "parent": 26}]}, "dependencies": [{"backtrace": 6, "id": "data_interface__rosidl_generator_c::@6890427a1f51a3e7e1df"}, {"backtrace": 9, "id": "data_interface__rosidl_typesupport_fastrtps_c::@6890427a1f51a3e7e1df"}, {"backtrace": 12, "id": "data_interface__cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 15, "id": "data_interface__rosidl_typesupport_fastrtps_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 18, "id": "data_interface__rosidl_typesupport_introspection_c::@6890427a1f51a3e7e1df"}, {"backtrace": 21, "id": "data_interface__rosidl_typesupport_c::@6890427a1f51a3e7e1df"}, {"backtrace": 24, "id": "data_interface__rosidl_typesupport_introspection_cpp::@6890427a1f51a3e7e1df"}, {"backtrace": 27, "id": "data_interface__rosidl_typesupport_cpp::@6890427a1f51a3e7e1df"}], "id": "data_interface::@6890427a1f51a3e7e1df", "name": "data_interface", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"name": "CMake Rules", "sourceIndexes": [12]}], "sources": [{"backtrace": 2, "path": "msg/PoseEst3d2dPointMsg.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "msg/PoseEst3d2dPointsMsg.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/PoseEst3d2dSrv.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/PoseEst3d2dSrv_Request.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/PoseEst3d2dSrv_Response.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/CameraImage.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/CameraImage_Request.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/CameraImage_Response.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "srv/InitPandaRobot.srv", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/InitPandaRobot_Request.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/rosidl_cmake/srv/InitPandaRobot_Response.msg", "sourceGroupIndex": 0}, {"backtrace": 2, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/CMakeFiles/data_interface", "sourceGroupIndex": 0}, {"backtrace": 0, "isGenerated": true, "path": "/home/<USER>/ccag/ccag_ws/build/data_interface/CMakeFiles/data_interface.rule", "sourceGroupIndex": 1}], "type": "UTILITY"}