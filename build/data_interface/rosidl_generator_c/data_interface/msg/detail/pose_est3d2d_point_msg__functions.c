// generated from rosidl_generator_c/resource/idl__functions.c.em
// with input from data_interface:msg/PoseEst3d2dPointMsg.idl
// generated code does not contain a copyright notice
#include "data_interface/msg/detail/pose_est3d2d_point_msg__functions.h"

#include <assert.h>
#include <stdbool.h>
#include <stdlib.h>
#include <string.h>

#include "rcutils/allocator.h"


bool
data_interface__msg__PoseEst3d2dPointMsg__init(data_interface__msg__PoseEst3d2dPointMsg * msg)
{
  if (!msg) {
    return false;
  }
  // x
  // y
  // label
  return true;
}

void
data_interface__msg__PoseEst3d2dPointMsg__fini(data_interface__msg__PoseEst3d2dPointMsg * msg)
{
  if (!msg) {
    return;
  }
  // x
  // y
  // label
}

bool
data_interface__msg__PoseEst3d2dPointMsg__are_equal(const data_interface__msg__PoseEst3d2dPointMsg * lhs, const data_interface__msg__PoseEst3d2dPointMsg * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  // x
  if (lhs->x != rhs->x) {
    return false;
  }
  // y
  if (lhs->y != rhs->y) {
    return false;
  }
  // label
  if (lhs->label != rhs->label) {
    return false;
  }
  return true;
}

bool
data_interface__msg__PoseEst3d2dPointMsg__copy(
  const data_interface__msg__PoseEst3d2dPointMsg * input,
  data_interface__msg__PoseEst3d2dPointMsg * output)
{
  if (!input || !output) {
    return false;
  }
  // x
  output->x = input->x;
  // y
  output->y = input->y;
  // label
  output->label = input->label;
  return true;
}

data_interface__msg__PoseEst3d2dPointMsg *
data_interface__msg__PoseEst3d2dPointMsg__create()
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  data_interface__msg__PoseEst3d2dPointMsg * msg = (data_interface__msg__PoseEst3d2dPointMsg *)allocator.allocate(sizeof(data_interface__msg__PoseEst3d2dPointMsg), allocator.state);
  if (!msg) {
    return NULL;
  }
  memset(msg, 0, sizeof(data_interface__msg__PoseEst3d2dPointMsg));
  bool success = data_interface__msg__PoseEst3d2dPointMsg__init(msg);
  if (!success) {
    allocator.deallocate(msg, allocator.state);
    return NULL;
  }
  return msg;
}

void
data_interface__msg__PoseEst3d2dPointMsg__destroy(data_interface__msg__PoseEst3d2dPointMsg * msg)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (msg) {
    data_interface__msg__PoseEst3d2dPointMsg__fini(msg);
  }
  allocator.deallocate(msg, allocator.state);
}


bool
data_interface__msg__PoseEst3d2dPointMsg__Sequence__init(data_interface__msg__PoseEst3d2dPointMsg__Sequence * array, size_t size)
{
  if (!array) {
    return false;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  data_interface__msg__PoseEst3d2dPointMsg * data = NULL;

  if (size) {
    data = (data_interface__msg__PoseEst3d2dPointMsg *)allocator.zero_allocate(size, sizeof(data_interface__msg__PoseEst3d2dPointMsg), allocator.state);
    if (!data) {
      return false;
    }
    // initialize all array elements
    size_t i;
    for (i = 0; i < size; ++i) {
      bool success = data_interface__msg__PoseEst3d2dPointMsg__init(&data[i]);
      if (!success) {
        break;
      }
    }
    if (i < size) {
      // if initialization failed finalize the already initialized array elements
      for (; i > 0; --i) {
        data_interface__msg__PoseEst3d2dPointMsg__fini(&data[i - 1]);
      }
      allocator.deallocate(data, allocator.state);
      return false;
    }
  }
  array->data = data;
  array->size = size;
  array->capacity = size;
  return true;
}

void
data_interface__msg__PoseEst3d2dPointMsg__Sequence__fini(data_interface__msg__PoseEst3d2dPointMsg__Sequence * array)
{
  if (!array) {
    return;
  }
  rcutils_allocator_t allocator = rcutils_get_default_allocator();

  if (array->data) {
    // ensure that data and capacity values are consistent
    assert(array->capacity > 0);
    // finalize all array elements
    for (size_t i = 0; i < array->capacity; ++i) {
      data_interface__msg__PoseEst3d2dPointMsg__fini(&array->data[i]);
    }
    allocator.deallocate(array->data, allocator.state);
    array->data = NULL;
    array->size = 0;
    array->capacity = 0;
  } else {
    // ensure that data, size, and capacity values are consistent
    assert(0 == array->size);
    assert(0 == array->capacity);
  }
}

data_interface__msg__PoseEst3d2dPointMsg__Sequence *
data_interface__msg__PoseEst3d2dPointMsg__Sequence__create(size_t size)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  data_interface__msg__PoseEst3d2dPointMsg__Sequence * array = (data_interface__msg__PoseEst3d2dPointMsg__Sequence *)allocator.allocate(sizeof(data_interface__msg__PoseEst3d2dPointMsg__Sequence), allocator.state);
  if (!array) {
    return NULL;
  }
  bool success = data_interface__msg__PoseEst3d2dPointMsg__Sequence__init(array, size);
  if (!success) {
    allocator.deallocate(array, allocator.state);
    return NULL;
  }
  return array;
}

void
data_interface__msg__PoseEst3d2dPointMsg__Sequence__destroy(data_interface__msg__PoseEst3d2dPointMsg__Sequence * array)
{
  rcutils_allocator_t allocator = rcutils_get_default_allocator();
  if (array) {
    data_interface__msg__PoseEst3d2dPointMsg__Sequence__fini(array);
  }
  allocator.deallocate(array, allocator.state);
}

bool
data_interface__msg__PoseEst3d2dPointMsg__Sequence__are_equal(const data_interface__msg__PoseEst3d2dPointMsg__Sequence * lhs, const data_interface__msg__PoseEst3d2dPointMsg__Sequence * rhs)
{
  if (!lhs || !rhs) {
    return false;
  }
  if (lhs->size != rhs->size) {
    return false;
  }
  for (size_t i = 0; i < lhs->size; ++i) {
    if (!data_interface__msg__PoseEst3d2dPointMsg__are_equal(&(lhs->data[i]), &(rhs->data[i]))) {
      return false;
    }
  }
  return true;
}

bool
data_interface__msg__PoseEst3d2dPointMsg__Sequence__copy(
  const data_interface__msg__PoseEst3d2dPointMsg__Sequence * input,
  data_interface__msg__PoseEst3d2dPointMsg__Sequence * output)
{
  if (!input || !output) {
    return false;
  }
  if (output->capacity < input->size) {
    const size_t allocation_size =
      input->size * sizeof(data_interface__msg__PoseEst3d2dPointMsg);
    rcutils_allocator_t allocator = rcutils_get_default_allocator();
    data_interface__msg__PoseEst3d2dPointMsg * data =
      (data_interface__msg__PoseEst3d2dPointMsg *)allocator.reallocate(
      output->data, allocation_size, allocator.state);
    if (!data) {
      return false;
    }
    // If reallocation succeeded, memory may or may not have been moved
    // to fulfill the allocation request, invalidating output->data.
    output->data = data;
    for (size_t i = output->capacity; i < input->size; ++i) {
      if (!data_interface__msg__PoseEst3d2dPointMsg__init(&output->data[i])) {
        // If initialization of any new item fails, roll back
        // all previously initialized items. Existing items
        // in output are to be left unmodified.
        for (; i-- > output->capacity; ) {
          data_interface__msg__PoseEst3d2dPointMsg__fini(&output->data[i]);
        }
        return false;
      }
    }
    output->capacity = input->size;
  }
  output->size = input->size;
  for (size_t i = 0; i < input->size; ++i) {
    if (!data_interface__msg__PoseEst3d2dPointMsg__copy(
        &(input->data[i]), &(output->data[i])))
    {
      return false;
    }
  }
  return true;
}
