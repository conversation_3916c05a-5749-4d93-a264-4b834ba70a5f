// generated from rosidl_generator_c/resource/idl__type_support.h.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TYPE_SUPPORT_H_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TYPE_SUPPORT_H_

#include "rosidl_typesupport_interface/macros.h"

#include "data_interface/msg/rosidl_generator_c__visibility_control.h"

#ifdef __cplusplus
extern "C"
{
#endif

#include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  data_interface,
  srv,
  InitPandaRobot_Request
)();

// already included above
// #include "rosidl_runtime_c/message_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_data_interface
const rosidl_message_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__MESSAGE_SYMBOL_NAME(
  rosidl_typesupport_c,
  data_interface,
  srv,
  InitPandaRobot_Response
)();

#include "rosidl_runtime_c/service_type_support_struct.h"

// Forward declare the get type support functions for this type.
ROSIDL_GENERATOR_C_PUBLIC_data_interface
const rosidl_service_type_support_t *
ROSIDL_TYPESUPPORT_INTERFACE__SERVICE_SYMBOL_NAME(
  rosidl_typesupport_c,
  data_interface,
  srv,
  InitPandaRobot
)();

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__TYPE_SUPPORT_H_
