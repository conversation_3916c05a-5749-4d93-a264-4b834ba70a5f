// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_H_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

// Include directives for member types
// Member 'request_string'
#include "rosidl_runtime_c/string.h"

/// Struct defined in srv/InitPandaRobot in the package data_interface.
typedef struct data_interface__srv__InitPandaRobot_Request
{
  rosidl_runtime_c__String request_string;
} data_interface__srv__InitPandaRobot_Request;

// Struct for a sequence of data_interface__srv__InitPandaRobot_Request.
typedef struct data_interface__srv__InitPandaRobot_Request__Sequence
{
  data_interface__srv__InitPandaRobot_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__InitPandaRobot_Request__Sequence;


// Constants defined in the message

// Include directives for member types
// Member 'response_string'
// already included above
// #include "rosidl_runtime_c/string.h"

/// Struct defined in srv/InitPandaRobot in the package data_interface.
typedef struct data_interface__srv__InitPandaRobot_Response
{
  rosidl_runtime_c__String response_string;
} data_interface__srv__InitPandaRobot_Response;

// Struct for a sequence of data_interface__srv__InitPandaRobot_Response.
typedef struct data_interface__srv__InitPandaRobot_Response__Sequence
{
  data_interface__srv__InitPandaRobot_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__InitPandaRobot_Response__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__STRUCT_H_
