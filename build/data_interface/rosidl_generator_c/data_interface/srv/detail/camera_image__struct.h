// generated from rosidl_generator_c/resource/idl__struct.h.em
// with input from data_interface:srv/CameraImage.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_H_
#define DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>


// Constants defined in the message

/// Struct defined in srv/CameraImage in the package data_interface.
typedef struct data_interface__srv__CameraImage_Request
{
  /// request from client
  bool get;
} data_interface__srv__CameraImage_Request;

// Struct for a sequence of data_interface__srv__CameraImage_Request.
typedef struct data_interface__srv__CameraImage_Request__Sequence
{
  data_interface__srv__CameraImage_Request * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__CameraImage_Request__Sequence;


// Constants defined in the message

// Include directives for member types
// Member 'server_image'
#include "sensor_msgs/msg/detail/image__struct.h"

/// Struct defined in srv/CameraImage in the package data_interface.
typedef struct data_interface__srv__CameraImage_Response
{
  /// img response from service
  sensor_msgs__msg__Image server_image;
} data_interface__srv__CameraImage_Response;

// Struct for a sequence of data_interface__srv__CameraImage_Response.
typedef struct data_interface__srv__CameraImage_Response__Sequence
{
  data_interface__srv__CameraImage_Response * data;
  /// The number of valid items in data
  size_t size;
  /// The number of allocated items in data
  size_t capacity;
} data_interface__srv__CameraImage_Response__Sequence;

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__SRV__DETAIL__CAMERA_IMAGE__STRUCT_H_
