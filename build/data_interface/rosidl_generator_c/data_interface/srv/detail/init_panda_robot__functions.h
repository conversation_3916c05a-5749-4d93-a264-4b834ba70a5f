// generated from rosidl_generator_c/resource/idl__functions.h.em
// with input from data_interface:srv/InitPandaRobot.idl
// generated code does not contain a copyright notice

#ifndef DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__FUNCTIONS_H_
#define DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__FUNCTIONS_H_

#ifdef __cplusplus
extern "C"
{
#endif

#include <stdbool.h>
#include <stdlib.h>

#include "rosidl_runtime_c/visibility_control.h"
#include "data_interface/msg/rosidl_generator_c__visibility_control.h"

#include "data_interface/srv/detail/init_panda_robot__struct.h"

/// Initialize srv/InitPandaRobot message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * data_interface__srv__InitPandaRobot_Request
 * )) before or use
 * data_interface__srv__InitPandaRobot_Request__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__init(data_interface__srv__InitPandaRobot_Request * msg);

/// Finalize srv/InitPandaRobot message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Request__fini(data_interface__srv__InitPandaRobot_Request * msg);

/// Create srv/InitPandaRobot message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * data_interface__srv__InitPandaRobot_Request__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
data_interface__srv__InitPandaRobot_Request *
data_interface__srv__InitPandaRobot_Request__create();

/// Destroy srv/InitPandaRobot message.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Request__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Request__destroy(data_interface__srv__InitPandaRobot_Request * msg);

/// Check for srv/InitPandaRobot message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__are_equal(const data_interface__srv__InitPandaRobot_Request * lhs, const data_interface__srv__InitPandaRobot_Request * rhs);

/// Copy a srv/InitPandaRobot message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__copy(
  const data_interface__srv__InitPandaRobot_Request * input,
  data_interface__srv__InitPandaRobot_Request * output);

/// Initialize array of srv/InitPandaRobot messages.
/**
 * It allocates the memory for the number of elements and calls
 * data_interface__srv__InitPandaRobot_Request__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__Sequence__init(data_interface__srv__InitPandaRobot_Request__Sequence * array, size_t size);

/// Finalize array of srv/InitPandaRobot messages.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Request__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Request__Sequence__fini(data_interface__srv__InitPandaRobot_Request__Sequence * array);

/// Create array of srv/InitPandaRobot messages.
/**
 * It allocates the memory for the array and calls
 * data_interface__srv__InitPandaRobot_Request__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
data_interface__srv__InitPandaRobot_Request__Sequence *
data_interface__srv__InitPandaRobot_Request__Sequence__create(size_t size);

/// Destroy array of srv/InitPandaRobot messages.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Request__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Request__Sequence__destroy(data_interface__srv__InitPandaRobot_Request__Sequence * array);

/// Check for srv/InitPandaRobot message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__Sequence__are_equal(const data_interface__srv__InitPandaRobot_Request__Sequence * lhs, const data_interface__srv__InitPandaRobot_Request__Sequence * rhs);

/// Copy an array of srv/InitPandaRobot messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Request__Sequence__copy(
  const data_interface__srv__InitPandaRobot_Request__Sequence * input,
  data_interface__srv__InitPandaRobot_Request__Sequence * output);

/// Initialize srv/InitPandaRobot message.
/**
 * If the init function is called twice for the same message without
 * calling fini inbetween previously allocated memory will be leaked.
 * \param[in,out] msg The previously allocated message pointer.
 * Fields without a default value will not be initialized by this function.
 * You might want to call memset(msg, 0, sizeof(
 * data_interface__srv__InitPandaRobot_Response
 * )) before or use
 * data_interface__srv__InitPandaRobot_Response__create()
 * to allocate and initialize the message.
 * \return true if initialization was successful, otherwise false
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__init(data_interface__srv__InitPandaRobot_Response * msg);

/// Finalize srv/InitPandaRobot message.
/**
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Response__fini(data_interface__srv__InitPandaRobot_Response * msg);

/// Create srv/InitPandaRobot message.
/**
 * It allocates the memory for the message, sets the memory to zero, and
 * calls
 * data_interface__srv__InitPandaRobot_Response__init().
 * \return The pointer to the initialized message if successful,
 * otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
data_interface__srv__InitPandaRobot_Response *
data_interface__srv__InitPandaRobot_Response__create();

/// Destroy srv/InitPandaRobot message.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Response__fini()
 * and frees the memory of the message.
 * \param[in,out] msg The allocated message pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Response__destroy(data_interface__srv__InitPandaRobot_Response * msg);

/// Check for srv/InitPandaRobot message equality.
/**
 * \param[in] lhs The message on the left hand size of the equality operator.
 * \param[in] rhs The message on the right hand size of the equality operator.
 * \return true if messages are equal, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__are_equal(const data_interface__srv__InitPandaRobot_Response * lhs, const data_interface__srv__InitPandaRobot_Response * rhs);

/// Copy a srv/InitPandaRobot message.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source message pointer.
 * \param[out] output The target message pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer is null
 *   or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__copy(
  const data_interface__srv__InitPandaRobot_Response * input,
  data_interface__srv__InitPandaRobot_Response * output);

/// Initialize array of srv/InitPandaRobot messages.
/**
 * It allocates the memory for the number of elements and calls
 * data_interface__srv__InitPandaRobot_Response__init()
 * for each element of the array.
 * \param[in,out] array The allocated array pointer.
 * \param[in] size The size / capacity of the array.
 * \return true if initialization was successful, otherwise false
 * If the array pointer is valid and the size is zero it is guaranteed
 # to return true.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__Sequence__init(data_interface__srv__InitPandaRobot_Response__Sequence * array, size_t size);

/// Finalize array of srv/InitPandaRobot messages.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Response__fini()
 * for each element of the array and frees the memory for the number of
 * elements.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Response__Sequence__fini(data_interface__srv__InitPandaRobot_Response__Sequence * array);

/// Create array of srv/InitPandaRobot messages.
/**
 * It allocates the memory for the array and calls
 * data_interface__srv__InitPandaRobot_Response__Sequence__init().
 * \param[in] size The size / capacity of the array.
 * \return The pointer to the initialized array if successful, otherwise NULL
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
data_interface__srv__InitPandaRobot_Response__Sequence *
data_interface__srv__InitPandaRobot_Response__Sequence__create(size_t size);

/// Destroy array of srv/InitPandaRobot messages.
/**
 * It calls
 * data_interface__srv__InitPandaRobot_Response__Sequence__fini()
 * on the array,
 * and frees the memory of the array.
 * \param[in,out] array The initialized array pointer.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
void
data_interface__srv__InitPandaRobot_Response__Sequence__destroy(data_interface__srv__InitPandaRobot_Response__Sequence * array);

/// Check for srv/InitPandaRobot message array equality.
/**
 * \param[in] lhs The message array on the left hand size of the equality operator.
 * \param[in] rhs The message array on the right hand size of the equality operator.
 * \return true if message arrays are equal in size and content, otherwise false.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__Sequence__are_equal(const data_interface__srv__InitPandaRobot_Response__Sequence * lhs, const data_interface__srv__InitPandaRobot_Response__Sequence * rhs);

/// Copy an array of srv/InitPandaRobot messages.
/**
 * This functions performs a deep copy, as opposed to the shallow copy that
 * plain assignment yields.
 *
 * \param[in] input The source array pointer.
 * \param[out] output The target array pointer, which must
 *   have been initialized before calling this function.
 * \return true if successful, or false if either pointer
 *   is null or memory allocation fails.
 */
ROSIDL_GENERATOR_C_PUBLIC_data_interface
bool
data_interface__srv__InitPandaRobot_Response__Sequence__copy(
  const data_interface__srv__InitPandaRobot_Response__Sequence * input,
  data_interface__srv__InitPandaRobot_Response__Sequence * output);

#ifdef __cplusplus
}
#endif

#endif  // DATA_INTERFACE__SRV__DETAIL__INIT_PANDA_ROBOT__FUNCTIONS_H_
