# 1.导包；
import sys
import rclpy
from rclpy.node import Node
from data_interface.srv import InitPandaRobot

# 3.定义节点类；
class InitPandaRobotPos(Node):
    def __init__(self):
        super().__init__('init_panda')
        # 3-1.创建客户端；
        self.cli = self.create_client(InitPandaRobot, 'init_panda_robot')
        # 3-2.等待服务连接；
        while not self.cli.wait_for_service(timeout_sec=1.0):
            self.get_logger().info('服务连接中，请稍候...')
        
        self.req = InitPandaRobot.Request()

    # 3-3.组织请求数据并发送；
    def send_request(self):
        self.req.request_string = 'Initialize the robot arm pose...' 
        self.future = self.cli.call_async(self.req)


def main():
    # 2.初始化 ROS2 客户端；
    rclpy.init()

    # 4.创建对象并调用其功能；
    init_panda = InitPandaRobotPos()
    init_panda.send_request()

    # 处理响应
    rclpy.spin_until_future_complete(init_panda, init_panda.future)
    try:
        response = init_panda.future.result()
    except Exception as e:
        init_panda.get_logger().info('服务请求失败： %r' % (e))
    else:
        init_panda.get_logger().info(f'响应结果：{response.response_string}')

    # 5.释放资源。
    rclpy.shutdown()


if __name__ == '__main__':
    main()
