import cv2
import time
import os
import glfw
import queue
import threading
import numpy as np
import mujoco, mujoco_viewer
from cv_bridge import CvBridge
from sensor_msgs.msg import Image, JointState
from trajectory_msgs.msg import JointTrajectory
from ament_index_python.packages import get_package_share_directory

import rclpy
from rclpy.node import Node
from tf2_ros import TransformBroadcaster
import tf_transformations
from geometry_msgs.msg import TransformStamped
from data_interface.srv import InitPandaRobot


class PandaSimulation(Node):
    def __init__(self):
        super().__init__("panda_simulation")
        self.package_dir = get_package_share_directory('mujoco_sim')
        self.xml_path = os.path.join(self.package_dir, "descriptions/panda_description/mjcf/scene.xml")
        # self.xml_path = "/home/<USER>/ccag/ccag_ws/src/mujoco_sim/mujoco_sim/descriptions/panda_description/mjcf/scene.xml"
        self.wrist_cam_img = None
        self.is_init_one = True

        # self.init_pos = [ 0.32, -0.435, -0.31, -2.582, -0.154, 2.356, 0.0]
        self.init_pos = [ 0.32, -0.435, -0.31, -2.94, -0.154, 2.81, 0.0]
        
        # 添加线程锁来同步图像数据
        self.img_lock = threading.Lock()
        self.img_ready = False
        
        # 添加轨迹比较变量
        self.last_trajectory = None
        self.trajectory_tolerance = 1e-6  # 轨迹比较的容差

        self.model = mujoco.MjModel.from_xml_path(self.xml_path)
        self.data = mujoco.MjData(self.model)
        

        # subscribe关节轨迹
        self.joint_sub = self.create_subscription(
            JointTrajectory,
            "/generated_trajectory",
            self.joint_states_callback,
            10
        )

        # 末端相机图像publisher
        self.img_pub = self.create_publisher(
            Image,
            "rgb_cam/image_raw",
            10
        )
        
        # JointState publisher
        self.joint_state_pub = self.create_publisher(
            JointState,
            "/joint_states",
            10
        )
        
        self.traj_queue = queue.Queue(1)
		
        self.joint_names = [
            "panda_joint1", "panda_joint2", "panda_joint3",
            "panda_joint4", "panda_joint5", "panda_joint6",
            "panda_joint7"
        ]

        # 创建服务端
        self.init_panda_pos = self.create_service(InitPandaRobot, 'init_panda_robot', self.init_panda_pos_callback)
        # 创建动态坐标变换发布方；
        # self.br = TransformBroadcaster(self)


    def init_panda_pos_callback(self, request, response):
        response.response_string = '收到初始化机械臂位姿请求，并已初始化完成......'
        self.is_init_one = True
        self.get_logger().info(f'self.is_init_one = {self.is_init_one}')
        return response
    
    def get_img_callback(self):
        # 检查图像是否准备好
        with self.img_lock:
            if not self.img_ready or self.wrist_cam_img is None:
                return
            
            # 创建图像的副本以避免线程冲突
            bgr = cv2.cvtColor(np.flipud(self.wrist_cam_img), cv2.COLOR_RGB2BGR)
            img_copy = bgr.copy()
        
        bridge = CvBridge()
        img = bridge.cv2_to_imgmsg(img_copy, encoding="bgr8")
        img.header.stamp = self.get_clock().now().to_msg()
        img.header.frame_id = "wrist_camera"
        self.img_pub.publish(img)


    def is_trajectory_different(self, new_trajectory):
        """比较新轨迹是否与上次轨迹不同"""
        if self.last_trajectory is None:
            return True
        
        # 检查形状是否相同
        if new_trajectory.shape != self.last_trajectory.shape:
            return True
        
        # 检查轨迹数据是否在容差范围内相同
        return not np.allclose(new_trajectory, self.last_trajectory, 
                              atol=self.trajectory_tolerance, rtol=self.trajectory_tolerance)

    def joint_states_callback(self, msg: JointTrajectory):
        # 解析时间点
        time_points=[]
        for pt in msg.points:
            t = pt.time_from_start.sec + pt.time_from_start.nanosec * 1e-9
            time_points.append(t)
        time_points = np.array(time_points)
         # 解析关节角度
        joint_trajectory=[]
        for pt in msg.points:
            joint_trajectory.append(pt.positions)
        joint_trajectory = np.array(joint_trajectory).T  # shape: [num_joints, num_points]
        
        if joint_trajectory.size == 0 or time_points.size == 0:
            self.get_logger().error("Trajectory message is empty.")
            return
        
        # 检查轨迹是否与上次不同
        if self.is_trajectory_different(joint_trajectory):
            # 清空队列，确保只有最新的轨迹
            while not self.traj_queue.empty():
                try:
                    self.traj_queue.get_nowait()
                except queue.Empty:
                    break
            
            # 放入新轨迹
            self.traj_queue.put(joint_trajectory)
            self.last_trajectory = joint_trajectory.copy()
            self.get_logger().info(f"New trajectory added to queue. Shape: {joint_trajectory.shape}")
        else:
            pass
            # self.get_logger().info("Trajectory is identical to previous one, skipping.")

    def run_visualization(self):
        # 渲染可视化界面
        viewer = mujoco_viewer.MujocoViewer(self.model, self.data)
        while viewer.is_alive:
            if self.traj_queue.empty():
                if self.is_init_one:
                    self.data.qpos[:7] = self.init_pos
                else:
                    self.data.qpos[:7] = joint_trajectory[:7, -1]
                viewer.render()
                self.camera_render()
                mujoco.mj_step(self.model, self.data)
            else:
                joint_trajectory = self.traj_queue.get()
                for idx in range(joint_trajectory.shape[1] - 1):
                    self.data.qpos[:7] = joint_trajectory[:7, idx]
                    mujoco.mj_step(self.model, self.data)
                    viewer.render()
                    # self.camera_render()
                self.is_init_one = False
                print(">> traj run finished, queue size: ",self.traj_queue.qsize())
    
        # cv2.destroyAllWindows()
        viewer.close()
        
    def publish_joint_state(self):
        """Publish current joint state from MuJoCo simulation"""
        # pos, T = self.get_end_effector_pose("link7")
        # print(pos)
        
        # tf = TransformStamped()
        # tf.header.stamp = self.get_clock().now().to_msg()
        # tf.header.frame_id = "link0"
        # tf.child_frame_id = "link7"
        # tf.transform.translation.x = pos[0]
        # tf.transform.translation.y = pos[1]
        # tf.transform.translation.z = pos[2]
        
        # quat = tf_transformations.quaternion_from_matrix(T)  # [x, y, z, w]
        # tf.transform.rotation.x = quat[0]
        # tf.transform.rotation.y = quat[1]
        # tf.transform.rotation.z = quat[2]
        # tf.transform.rotation.w = quat[3]
        
        # self.br.sendTransform(tf)
        
        try:
            msg = JointState()
            msg.header.stamp = self.get_clock().now().to_msg()
            msg.header.frame_id = "base_link"
            msg.name = self.joint_names
            
            # Convert numpy arrays to Python lists
            msg.position = list(self.data.qpos.copy().astype(float))
            msg.velocity = list(self.data.qvel.copy().astype(float))
            
            msg.effort = []  # MuJoCo不直接提供effort数据
            
            # Validate data lengths
            if len(msg.position) != len(msg.name) or len(msg.velocity) != len(msg.name):
                self.get_logger().error(
                    f"Data length mismatch: positions({len(msg.position)}), "
                    f"velocities({len(msg.velocity)}), joints({len(msg.name)})"
                )
                return
                
            self.joint_state_pub.publish(msg)
            # pos, mat = self.get_end_effector_pose("link7")
            # print(pos)
            # print(mat)
        except Exception as e:
            self.get_logger().error(f"Error in publish_joint_state: {str(e)}")
            
    # def get_end_effector_pose(self, end_effector_name="panda_joint7"):
    #     # 获取末端link的body id
    #     end_effector_id = self.model.body(end_effector_name).id
    #     # 位置和旋转
    #     pos = self.data.xpos[end_effector_id]  # (3,)
    #     mat = self.data.xmat[end_effector_id].reshape(3, 3)  # (3,3)
    #     # 4x4变换矩阵
    #     # T = np.eye(4)
    #     # T[:3, :3] = mat
    #     # T[:3, 3] = pos
    #     # print(T)
    #     return pos, mat
    
    def camera_render(self):
        # 渲染末端RGB相机视角
        glfw.init
        wrist_cam_id = self.data.cam("wrist_cam").id
        wrist_cam = mujoco.MjvCamera()
        wrist_cam.fixedcamid = wrist_cam_id
        wrist_cam.type = mujoco.mjtCamera.mjCAMERA_FIXED
        if wrist_cam_id != -1:
            wrist_cam.fixedcamid = wrist_cam_id
        
        ctx = mujoco.MjrContext(self.model, mujoco.mjtFontScale.mjFONTSCALE_150.value)
        scn = mujoco.MjvScene(self.model, maxgeom=10000)
        # 渲染末端相机的图像
        viewport = mujoco.MjrRect(0, 0 , 1920, 1080)
        mujoco.mjv_updateScene(self.model, self.data, mujoco.MjvOption(), 
                                    mujoco.MjvPerturb(), wrist_cam, 
                                    mujoco.mjtCatBit.mjCAT_ALL.value, scn)
        mujoco.mjr_render(viewport, scn, ctx)
        
        # 使用线程锁保护图像数据更新
        with self.img_lock:
            self.wrist_cam_img = np.zeros((1080, 1920, 3), dtype=np.uint8)
            mujoco.mjr_readPixels(self.wrist_cam_img, None, viewport, ctx)
            self.img_ready = True
        
        # bgr = cv2.cvtColor(np.flipud(self.wrist_cam_img), cv2.COLOR_RGB2BGR)
        # cv2.imshow('MuJoCo Camera Output', bgr)
        # cv2.waitKey(1)      


def main(args=None):
    rclpy.init(args=args)
    node = PandaSimulation()

    # 启动可视化线程
    t = threading.Thread(target=node.run_visualization)
    t.daemon = True  # 设置为守护线程
    t.start()
    
    # 等待一小段时间确保可视化线程开始运行
    time.sleep(1)
    
    # 启动ROS定时器（在可视化线程启动后）
    node.timer = node.create_timer(0.002, node.get_img_callback)
    node.timer = node.create_timer(0.002, node.publish_joint_state)

    rclpy.spin(node)
    rclpy.shutdown()


if __name__ == '__main__':
    main()