#include <rclcpp/rclcpp.hpp>
#include <mutex>
#include <chrono>
#include <thread>
#include <message_filters/subscriber.hpp>
#include <message_filters/time_synchronizer.hpp>
#include <message_filters/sync_policies/approximate_time.hpp>
#include <message_filters/sync_policies/exact_time.hpp>
#include <message_filters/synchronizer.hpp>
#include <cv_bridge/cv_bridge.h>
#include <sensor_msgs/msg/image.hpp>
#include <opencv2/opencv.hpp>
#include <vision_msgs/msg/bounding_box2_d.hpp>
#include <vision_msgs/msg/bounding_box2_d_array.hpp>
#include <geometry_msgs/msg/pose_with_covariance_stamped.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <yaml-cpp/yaml.h>
#include <tf2/LinearMath/Quaternion.h>
#include <tf2_ros/buffer.h>
#include <tf2_ros/transform_listener.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.h>
#include <geometry_msgs/msg/transform_stamped.hpp>
#include <ament_index_cpp/get_package_share_directory.hpp>
// #include <std_srvs/srv/empty.hpp>
// #include <std_srvs/srv/trigger.hpp>
#include "data_interface/srv/pose_est3d2d_srv.hpp"
#include "data_interface/msg/pose_est3d2d_points_msg.hpp"

using namespace std::chrono_literals;
using std::placeholders::_1;
using std::placeholders::_2;


// 相机参数结构体
struct CameraInfo {
    int height;
    int width;
    std::string distortion_model;
    std::vector<double> d;  // 畸变系数
    std::vector<double> k;  // 相机内参矩阵
    std::vector<double> r;  // 旋转矩阵
    std::vector<double> p;  // 投影矩阵
};


// 自定义节点类
class DetectedPostProcessing : public rclcpp::Node {
public:
  DetectedPostProcessing() : Node("post_processing") {
    // 读取相机参数
    std::string package_dir = ament_index_cpp::get_package_share_directory("detected_post_processing");
    std::string yaml_file = package_dir + "/config/camera_info.yaml";
    // std::string yaml_file = "/home/<USER>/ccag/ccag_ws/src/detected_post_processing/config/camera_info.yaml";
    try {
      YAML::Node config = YAML::LoadFile(yaml_file);
      
      // 读取并存储相机参数
      camera_info_.height = config["height"].as<int>();
      camera_info_.width = config["width"].as<int>();
      camera_info_.distortion_model = config["distortion_model"].as<std::string>();
      camera_info_.d = config["d"].as<std::vector<double>>();
      camera_info_.k = config["k"].as<std::vector<double>>();
      camera_info_.r = config["r"].as<std::vector<double>>();
      camera_info_.p = config["p"].as<std::vector<double>>();

      // 打印读取到的参数
      // RCLCPP_INFO(this->get_logger(), "Camera Info:");
      // RCLCPP_INFO(this->get_logger(), "Resolution: %dx%d", camera_info_.width, camera_info_.height);
      // RCLCPP_INFO(this->get_logger(), "Distortion model: %s", camera_info_.distortion_model.c_str());
      
      // RCLCPP_INFO(this->get_logger(), "Camera matrix (k):");
      // for(int i = 0; i < 3; i++) {
      //   RCLCPP_INFO(this->get_logger(), "%f %f %f", 
      //     camera_info_.k[i*3], camera_info_.k[i*3+1], camera_info_.k[i*3+2]);
      // }
      
      // RCLCPP_INFO(this->get_logger(), "Distortion coefficients (d):");
      // for(const auto& d : camera_info_.d) {
      //   RCLCPP_INFO(this->get_logger(), "%f", d);
      // }

    } catch (const YAML::Exception& e) {
      RCLCPP_ERROR(this->get_logger(), "Error reading YAML file: %s", e.what());
    } catch (const std::exception& e) {
      RCLCPP_ERROR(this->get_logger(), "Error reading YAML file: %s", e.what());
    }

    sub_image_ = std::make_shared<message_filters::Subscriber<sensor_msgs::msg::Image>>(this, "/rgb_cam/image_raw");
    sub_bbox_ = std::make_shared<message_filters::Subscriber<vision_msgs::msg::BoundingBox2DArray>>(this, "/yolo/charging_post_bbox");

    pub_charge_pose_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/charging_post_pose", 1);

    using SyncPolicy = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, vision_msgs::msg::BoundingBox2DArray>;
    sync_ = std::make_shared<message_filters::Synchronizer<SyncPolicy>>(SyncPolicy(10), *sub_image_, *sub_bbox_);
    sync_->registerCallback(&DetectedPostProcessing::sync_callback, this);

    // 临时发布充电口位姿
    // timer_ = this->create_wall_timer(100ms, std::bind(&DetectedPostProcessing::timer_callback, this));

    // 监听机械臂发布的link0到link7的tf变换（目前这部分计算放到motion_planning.py里面去了）
    // tf_buffer_ = std::make_shared<tf2_ros::Buffer>(this->get_clock());
    // tf_listener_ = std::make_shared<tf2_ros::TransformListener>(*tf_buffer_);
    // tf_timer_ = this->create_wall_timer(500ms, std::bind(&DetectedPostProcessing::tf_timer_callback, this));

    // 创建客户端
    // client_ = this->create_client<data_interface::srv::PoseEst3d2dSrv>("pose_estimation_3d2d");
    RCLCPP_INFO(this->get_logger(), "====================== DetectedPostProcessing init...");

  }

private:
  // 客户端发布数据 - 异步版本，带请求控制
  bool send_request_async(const data_interface::msg::PoseEst3d2dPointsMsg & points) {
      // 检查是否已经完成过请求（一次性请求模式）
      if (service_request_completed_) {
          RCLCPP_DEBUG(get_logger(), "Service request already completed, skipping...");
          return false;
      }
      // 检查是否有请求正在处理
      if (service_request_pending_) {
          RCLCPP_DEBUG(get_logger(), "Service request already pending, skipping...");
          return false;
      }
      // 检查冷却时间（防止频繁请求）
      auto now = std::chrono::steady_clock::now();
      if (now - last_request_time_ < REQUEST_COOLDOWN) {
          RCLCPP_DEBUG(get_logger(), "Request cooldown active, skipping...");
          return false;
      }
      const int max_attempts = 5;  // 最大尝试次数
      int attempts = 0;           // 当前尝试次数
      // 确保服务可用（有限次尝试）
      while (attempts < max_attempts && !client_->wait_for_service(1s)) {
          RCLCPP_WARN(get_logger(), "Service not available, waiting... (%d/%d)",
                        attempts + 1, max_attempts);
          attempts++;
      }
      // 检查是否超过最大尝试次数
      if (attempts >= max_attempts) {
          RCLCPP_ERROR(get_logger(), "Service unavailable after %d attempts. Exiting.", max_attempts);
          return false;
      }
      // 创建服务请求
      auto request = std::make_shared<data_interface::srv::PoseEst3d2dSrv::Request>();
      request->points = points;  // 使用服务请求中定义的实际字段名
      // 设置请求状态
      service_request_pending_ = true;
      last_request_time_ = now;
      
      // 发送异步请求并设置回调
      // 睡眠1秒钟
      // std::this_thread::sleep_for(std::chrono::seconds(1));
      auto future = client_->async_send_request(request, std::bind(&DetectedPostProcessing::service_response_callback, this, std::placeholders::_1));
      RCLCPP_INFO(get_logger(), "Service request sent successfully");
      return true;
  }
  // 服务响应回调函数
  void service_response_callback(rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedFuture future) {
      // 重置请求状态
      service_request_pending_ = false;
      try {
          auto response = future.get();
          // auto pose = response->pose;
          geometry_msgs::msg::PoseStamped pose_msg = response->pose;
          // // ===============
          // cv::Mat T_cam_obj = cv::Mat::eye(4, 4, CV_64F);
          // // 提取位置信息
          // T_cam_obj.at<double>(0, 3) = pose.pose.position.x;
          // T_cam_obj.at<double>(1, 3) = pose.pose.position.y;
          // T_cam_obj.at<double>(2, 3) = pose.pose.position.z;
          // // 提取四元数
          // tf2::Quaternion q_cam_obj(
          //     pose.pose.orientation.x,
          //     pose.pose.orientation.y,
          //     pose.pose.orientation.z,
          //     pose.pose.orientation.w
          // );
          // // 转换为旋转矩阵
          // tf2::Matrix3x3 rot_matrix(q_cam_obj);
          // // 填充旋转部分
          // for (int i = 0; i < 3; i++) {
          //     for (int j = 0; j < 3; j++) {
          //         T_cam_obj.at<double>(i, j) = rot_matrix[i][j];
          //     }
          // }
          // // std::cout << "T_base_end = \n" << T_base_end << std::endl;
          // std::cout << "T_cam_obj = \n" << T_cam_obj << std::endl;
          // // 2. T_base_cam * T_obj_cam
          // // cv::Mat T_base_obj = T_base_end * T_cam_obj;
          // // std::cout << "T_base_obj = \n" << T_base_obj << std::endl;
          // // 3. 提取
          // double x = T_base_obj.at<double>(0, 3);
          // double y = T_base_obj.at<double>(1, 3);
          // double z = T_base_obj.at<double>(2, 3);
          // cv::Mat R_obj_base = T_base_obj(cv::Rect(0, 0, 3, 3)).clone();
          // tf2::Matrix3x3 tf_R(
          //     R_obj_base.at<double>(0,0), R_obj_base.at<double>(0,1), R_obj_base.at<double>(0,2),
          //     R_obj_base.at<double>(1,0), R_obj_base.at<double>(1,1), R_obj_base.at<double>(1,2),
          //     R_obj_base.at<double>(2,0), R_obj_base.at<double>(2,1), R_obj_base.at<double>(2,2)
          // );
          // tf2::Quaternion q_base_obj;
          // tf_R.getRotation(q_base_obj);
          // // 4. 填充
          // geometry_msgs::msg::PoseStamped pose_msg;
          // pose_msg.pose.position.x = x;
          // pose_msg.pose.position.y = y;
          // pose_msg.pose.position.z = z;
          // pose_msg.pose.orientation.x = q_base_obj.x();
          // pose_msg.pose.orientation.y = q_base_obj.y();
          // pose_msg.pose.orientation.z = q_base_obj.z();
          // pose_msg.pose.orientation.w = q_base_obj.w();
          // RCLCPP_INFO(this->get_logger(), "============charge_pose_msg. . .");
          // RCLCPP_INFO(this->get_logger(), "x: %f", pose_msg.pose.position.x);
          // RCLCPP_INFO(this->get_logger(), "y: %f", pose_msg.pose.position.y);
          // RCLCPP_INFO(this->get_logger(), "z: %f", pose_msg.pose.position.z);
          // RCLCPP_INFO(this->get_logger(), "qx: %f", pose_msg.pose.orientation.x);
          // RCLCPP_INFO(this->get_logger(), "qy: %f", pose_msg.pose.orientation.y);
          // RCLCPP_INFO(this->get_logger(), "qz: %f", pose_msg.pose.orientation.z);
          // RCLCPP_INFO(this->get_logger(), "qw: %f", pose_msg.pose.orientation.w);
          // 5. 设置消息头
          pose_msg.header.frame_id = "camera_frame";
          pose_msg.header.stamp = this->get_clock()->now();
          pub_charge_pose_->publish(pose_msg);
          // 标记请求已完成（如果只需要请求一次）
          service_request_completed_ = true;
          RCLCPP_INFO(this->get_logger(), "Service request completed successfully");
      } catch (const std::exception& e) {
          RCLCPP_ERROR(this->get_logger(), "Service call exception: %s", e.what());
          // 请求失败时不标记为完成，允许重试
      }
  }
  // 重置服务请求状态（如果需要重新开始请求）
  void reset_service_request_state() {
      service_request_pending_ = false;
      service_request_completed_ = false;
      last_request_time_ = std::chrono::steady_clock::time_point{};
      RCLCPP_INFO(this->get_logger(), "Service request state reset - ready for new requests");
  }
  void timer_callback() {
    geometry_msgs::msg::PoseStamped charge_pose_msg;
    charge_pose_msg.pose.position.x = 0.60;
    charge_pose_msg.pose.position.y = -0.035;
    charge_pose_msg.pose.position.z = 0.380;
    charge_pose_msg.pose.orientation.x = 0.7070533;
    charge_pose_msg.pose.orientation.y = 0.7070533;
    charge_pose_msg.pose.orientation.z = 0.0086968;
    charge_pose_msg.pose.orientation.w = 0.0086968;
    charge_pose_msg.header.frame_id = "charge_post";
    charge_pose_msg.header.stamp = this->get_clock()->now();
    pub_charge_pose_->publish(charge_pose_msg);
  }
  // 订阅 link7==>link0 的 TF 变换
  void tf_timer_callback() {
    try {
        auto tf = tf_buffer_->lookupTransform("base_link", "plug", tf2::TimePointZero);
        double x = tf.transform.translation.x;
        double y = tf.transform.translation.y;
        double z = tf.transform.translation.z;

        double qx = tf.transform.rotation.x;
        double qy = tf.transform.rotation.y;
        double qz = tf.transform.rotation.z;
        double qw = tf.transform.rotation.w;

        tf2::Quaternion q(qx, qy, qz, qw);
        tf2::Matrix3x3 m(q);
        for (int i = 0; i < 3; ++i)
          for (int j = 0; j < 3; ++j)
            T_base_end.at<double>(i, j) = m[i][j];
        T_base_end.at<double>(0, 3) = x;
        T_base_end.at<double>(1, 3) = y;
        T_base_end.at<double>(2, 3) = z;
    } catch (tf2::TransformException &ex) {
        RCLCPP_WARN(this->get_logger(), "Could not transform base_link to plug: %s", ex.what());
    }
  }
  // 定义比较函数
  static bool compareByRadius(const cv::Vec3f& a, const cv::Vec3f& b) {
      return a[2] < b[2];  // 比较半径
  }
  // 像素坐标到相机坐标的转换函数
  cv::Point2d px2xy(const cv::Point2d& point, const std::vector<double>& camera_k, const std::vector<double>& camera_d, double z ) {
      // 将相机内参矩阵K和相机畸变参数D转换为OpenCV矩阵
      cv::Mat K = (cv::Mat_<double>(3, 3) << 
          camera_k[0], camera_k[1], camera_k[2],
          camera_k[3], camera_k[4], camera_k[5],
          camera_k[6], camera_k[7], camera_k[8]);
      
      cv::Mat D = (cv::Mat_<double>(1, 5) << 
          camera_d[0], camera_d[1], camera_d[2], camera_d[3], camera_d[4]);
      
      // 将输入点转换为OpenCV格式
      std::vector<cv::Point2d> points = {point};
      std::vector<cv::Point2d> undistorted_points;
      
      // 使用OpenCV的undistortPoints函数进行畸变矫正
      cv::undistortPoints(points, undistorted_points, K, D);
      
      // 乘以深度值z
      cv::Point2d result = undistorted_points[0] * z;
      
      return result;
  }
  // 整理图像中圆心的坐标顺序
  std::vector<cv::Point2f> image_points_sort(const std::vector<cv::Vec3f> &circles, const cv::Point2d &pt1){
    // 转换为 cv::Mat 类型用于 kmeans
    cv::Mat data(circles.size(), 1, CV_32F);
    for (size_t i = 0; i < circles.size(); ++i) {
        data.at<float>(i, 0) = circles[i][1];
    }
    // 聚类结果
    int K = 2;
    cv::Mat labels;
    cv::Mat centersMat;
    // 运行 K-means
    cv::kmeans(data, K, labels,
               cv::TermCriteria(cv::TermCriteria::EPS + cv::TermCriteria::MAX_ITER, 10, 1.0),
               3, cv::KMEANS_PP_CENTERS, centersMat);
    // 将聚类结果分组
    std::vector<cv::Point2f> groupTop, groupBottom;
    std::vector<float> yAvg(K, 0);
    std::vector<int> yCount(K, 0);

    // 计算每个簇的平均 y 值
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        yAvg[label] += circles[i][1];
        yCount[label]++;
    }
    for (int i = 0; i < K; ++i) {
        yAvg[i] /= yCount[i];
    }
    // 判断哪个是上（y 坐标小的是上面）
    int topLabel = yAvg[0] < yAvg[1] ? 0 : 1;
    int bottomLabel = 1 - topLabel;

    // 分开存放
    for (int i = 0; i < labels.rows; ++i) {
        int label = labels.at<int>(i);
        if (label == topLabel)
            groupTop.push_back(cv::Point2f(circles[i][0], circles[i][1]));
        else
            groupBottom.push_back(cv::Point2f(circles[i][0], circles[i][1]));
    }
    // std::cout << "groupTop.size(): " << groupTop.size() << " groupBottom.size(): " << groupBottom.size() << std::endl;
    std::sort(groupTop.begin(), groupTop.end(), [](const cv::Point2f& a, const cv::Point2f& b) {
        return a.x < b.x;  // 按x坐标升序排序
    });
    std::sort(groupBottom.begin(), groupBottom.end(), [](const cv::Point2f& a, const cv::Point2f& b) {
        return a.x < b.x;  // 按x坐标升序排序
    });
    // 定义2D点存放顺序
    std::vector<cv::Point2f> image_points;
    for (const auto& pt : groupTop) {
        image_points.push_back(cv::Point2d(pt1.x + pt.x, pt1.y + pt.y));
    }
    for (const auto& pt : groupBottom) {
        image_points.push_back(cv::Point2d(pt1.x + pt.x, pt1.y + pt.y));
    }
    // std::cout << "image_points:\n";
    // for (const auto& pt : image_points) {
    //     std::cout << "(" << pt.x << ", " << pt.y << ")\n";
    // }
    
    return image_points;
  }
  // 计算 pnp 并发布
  void pub_pnp_pose(const cv::Mat& rvec, const cv::Mat& tvec) {
      // 1. 将旋转向量转换为旋转矩阵
      cv::Mat rotation_matrix;
      cv::Rodrigues(rvec, rotation_matrix);
      // // 求逆，正交矩阵逆就是转置
      // // rotation_matrix = rotation_matrix.t();
      // cv::Mat T_cam_obj = cv::Mat::eye(4, 4, CV_64F);
      // rotation_matrix.copyTo(T_cam_obj(cv::Rect(0, 0, 3, 3)));
      // T_cam_obj.at<double>(0, 3) = tvec.at<double>(0);
      // T_cam_obj.at<double>(1, 3) = tvec.at<double>(1);
      // T_cam_obj.at<double>(2, 3) = tvec.at<double>(2);
      // std::cout << "T_base_end = \n" << T_base_end << std::endl;
      // std::cout << "T_cam_obj = \n" << T_cam_obj << std::endl;
      // // 2. T_base_cam * T_obj_cam
      // cv::Mat T_base_obj = T_base_end * T_cam_obj;
      // std::cout << "T_base_obj = \n" << T_base_obj << std::endl;
      // // 3. 提取
      // double x = T_base_obj.at<double>(0, 3);
      // double y = T_base_obj.at<double>(1, 3);
      // double z = T_base_obj.at<double>(2, 3);
      // cv::Mat R_obj_base = T_base_obj(cv::Rect(0, 0, 3, 3)).clone();
      tf2::Matrix3x3 tf_R(
          rotation_matrix.at<double>(0,0), rotation_matrix.at<double>(0,1), rotation_matrix.at<double>(0,2),
          rotation_matrix.at<double>(1,0), rotation_matrix.at<double>(1,1), rotation_matrix.at<double>(1,2),
          rotation_matrix.at<double>(2,0), rotation_matrix.at<double>(2,1), rotation_matrix.at<double>(2,2)
      );
      tf2::Quaternion q;
      tf_R.getRotation(q);
      // 4. 填充
      geometry_msgs::msg::PoseStamped pose_msg;
      pose_msg.pose.position.x = tvec.at<double>(0);
      pose_msg.pose.position.y = tvec.at<double>(1);
      pose_msg.pose.position.z = tvec.at<double>(2);
      pose_msg.pose.orientation.x = q.x();
      pose_msg.pose.orientation.y = q.y();
      pose_msg.pose.orientation.z = q.z();
      pose_msg.pose.orientation.w = q.w();

      RCLCPP_INFO(this->get_logger(), "============charge_pose_msg. . .");
      RCLCPP_INFO(this->get_logger(), "x: %f", pose_msg.pose.position.x);
      RCLCPP_INFO(this->get_logger(), "y: %f", pose_msg.pose.position.y);
      RCLCPP_INFO(this->get_logger(), "z: %f", pose_msg.pose.position.z);
      RCLCPP_INFO(this->get_logger(), "qx: %f", pose_msg.pose.orientation.x);
      RCLCPP_INFO(this->get_logger(), "qy: %f", pose_msg.pose.orientation.y);
      RCLCPP_INFO(this->get_logger(), "qz: %f", pose_msg.pose.orientation.z);
      RCLCPP_INFO(this->get_logger(), "qw: %f", pose_msg.pose.orientation.w);
      // 5. 设置消息头
      pose_msg.header.frame_id = "camera_frame";
      pose_msg.header.stamp = this->get_clock()->now();
      pub_charge_pose_->publish(pose_msg);
  }
  // 话题同步回调函数
  void sync_callback(const sensor_msgs::msg::Image::SharedPtr msg_image, const vision_msgs::msg::BoundingBox2DArray::SharedPtr msg_bbox) {
    // RCLCPP_INFO(this->get_logger(), "Received image and bounding box");
    // 错误检查：确保消息不为空
    if (!msg_image) {
      RCLCPP_WARN(this->get_logger(), "Received null image message");
      return;
    }
    if (!msg_bbox) {
      RCLCPP_WARN(this->get_logger(), "Received null bounding box message");
      return;
    }
    // 检查边界框数组是否为空
    if (msg_bbox->boxes.empty()) {
      RCLCPP_WARN(this->get_logger(), "Bounding box array is empty");
      return;
    }
    // 检查图像尺寸是否合理
    if (msg_image->width <= 0 || msg_image->height <= 0) {
      RCLCPP_WARN(this->get_logger(), "Invalid image dimensions: %dx%d", msg_image->width, msg_image->height);
      return;
    }
    // Convert ROS image message to OpenCV format
    cv_bridge::CvImagePtr cv_ptr;
    try {
      cv_ptr = cv_bridge::toCvCopy(msg_image, sensor_msgs::image_encodings::BGR8);
    } catch (cv_bridge::Exception& e) {
      RCLCPP_ERROR(this->get_logger(), "cv_bridge exception: %s", e.what());
      return;
    } catch (const std::exception& e) {
      RCLCPP_ERROR(this->get_logger(), "Exception during image conversion: %s", e.what());
      return;
    }
    
    if (!cv_ptr || cv_ptr->image.empty()) {
      RCLCPP_WARN(this->get_logger(), "Failed to convert image or image is empty");
      return;
    }
    cv::Mat cv_image = cv_ptr->image;
    // cv::imwrite("/home/<USER>/ccag/ccag_ws/src/detected_post_processing/cv_image.png", cv_image);
    // std::cout << "rows: " << cv_image.rows << ", cols: " << cv_image.cols << std::endl;
    // 左上角点：pt1，右下角点：pt2
    cv::Point2d pt1((int)(msg_bbox->boxes[0].center.position.x - msg_bbox->boxes[0].size_x / 2), (int)(msg_bbox->boxes[0].center.position.y - msg_bbox->boxes[0].size_y / 2));
    cv::Point2d pt2((int)(msg_bbox->boxes[0].center.position.x + msg_bbox->boxes[0].size_x / 2), (int)(msg_bbox->boxes[0].center.position.y + msg_bbox->boxes[0].size_y / 2));
    
    // 检查边界框是否在图像范围内
    if (pt1.x < 0 || pt1.y < 0 || pt2.x >= cv_image.cols || pt2.y >= cv_image.rows) {
      RCLCPP_WARN(this->get_logger(), "Bounding box out of image bounds: pt1(%f,%f), pt2(%f,%f), image(%d,%d)", 
                  pt1.x, pt1.y, pt2.x, pt2.y, cv_image.cols, cv_image.rows);
      return;
    }
    
    // 检查边界框大小是否合理
    if (pt2.x <= pt1.x || pt2.y <= pt1.y) {
      RCLCPP_WARN(this->get_logger(), "Invalid bounding box size: pt1(%f,%f), pt2(%f,%f)", pt1.x, pt1.y, pt2.x, pt2.y);
      return;
    }
    
    // 提取矩形区域（ROI）
    // cv::Mat roi = cv_image(cv::Rect(pt1, pt2));// 这是引用
    cv::Mat roi = cv_image(cv::Rect(pt1, pt2)).clone(); // 这是复制
    // cv::imwrite("/home/<USER>/ccag/ccag_ws/src/detected_post_processing/roi.png", roi);
    cv::Mat gray_roi;
    cv::cvtColor(roi, gray_roi, cv::COLOR_BGR2GRAY);
    // 高斯模糊 去除噪声
    // cv::GaussianBlur(gray_roi, gray_roi, cv::Size(3, 3), 0);
    // 显示边缘检测结果
    cv::Mat edges;
    cv::Canny(gray_roi, edges, 60, 150);
    // cv::imshow("Edges", edges);
    // ======================================================== 轮廓检测 ================================================
    // 2. 查找轮廓
    // std::vector<std::vector<cv::Point>> contours;
    // std::vector<cv::Vec4i> hierarchy;
    // cv::findContours(edges, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_SIMPLE);

    // // 霍夫圆变换检测圆，充电口五个大孔的半径约为 24
    std::vector<cv::Vec3f> circles;
    cv::HoughCircles(gray_roi, circles, cv::HOUGH_GRADIENT, 1, 30, 100, 30, 8, 20);
    if (circles.size() != 0){
      for(size_t i = 0; i < circles.size(); i++){
        cv::Point center(cvRound(circles[i][0]), cvRound(circles[i][1]));
        int radius = cvRound(circles[i][2]);
        // 画出圆心
        cv::circle(roi, center, 2, cv::Scalar(0, 255, 0), 2);
        // 画出圆
        cv::circle(roi, center, radius, cv::Scalar(0, 0, 255), 2);
        // std::cout << "circle " << i << " center = ( " << center.x << ", " << center.y << " ), radius = " << radius << std::endl;
      }
    }
    if (circles.size() != 5){
      cv::imshow("ROI", roi);
      cv::waitKey(1);
      // RCLCPP_INFO(this->get_logger(), "Hough circle detection cannot detect 5 circles. Please adjust the initial position of the robot arm. . .");
    }else{
      // 定义3D点，同一平面，z=0
      std::vector<cv::Point3f> object_points;
      object_points.push_back(cv::Point3f(0.016, 0, 0));
      object_points.push_back(cv::Point3f(0, 0, 0)); // 圆心
      object_points.push_back(cv::Point3f(-0.016, 0, 0));
      object_points.push_back(cv::Point3f(0.008, 0.0139, 0));
      object_points.push_back(cv::Point3f(-0.008, 0.0139, 0));
      // 定义图像中的目标点顺序
      std::vector<cv::Point2f> image_points = image_points_sort(circles, pt1);
      // image_points.erase(image_points.begin() + 1);
      // 在原始图像中绘制这5个圆心点
      for (size_t i = 0; i < image_points.size(); ++i) {
        cv::Point center(cvRound(image_points[i].x), cvRound(image_points[i].y));
        // 画出圆心（绿色，半径为3）
        cv::circle(cv_image, center, 2, cv::Scalar(0, 0, 255), 2);
        // 添加编号标签
        cv::putText(cv_image, std::to_string(i), center + cv::Point(5, 5), 
                   cv::FONT_HERSHEY_SIMPLEX, 0.5, cv::Scalar(255, 0, 0), 1);
      }
      cv::imshow("cv_image", cv_image);

      // 检查键盘输入
      int key = cv::waitKey(1) & 0xFF;
      if (key == 'r' || key == 'R') {
          RCLCPP_INFO(this->get_logger(), "Reset key pressed - resetting request state");
          reset_service_request_state();
      } else if (key == 's' || key == 'S') {
          // 显示状态
          std::string status;
          if (service_request_pending_) {
              status = "PENDING";
          } else if (service_request_completed_) {
              status = "COMPLETED";
          } else {
              status = "READY";
          }
          RCLCPP_INFO(this->get_logger(), "Current request status: %s", status.c_str());
      }
      //========================================
      data_interface::msg::PoseEst3d2dPointsMsg points_msg;
      for (size_t i = 0; i < image_points.size(); ++i) {
        points_msg.points[i].x = image_points[i].x;
        points_msg.points[i].y = image_points[i].y;
        points_msg.points[i].label = i;
      }
      // 发送异步请求
      // send_request_async(points_msg);
      // 注意：响应将在 service_response_callback 中处理
      //=========================================
      // 相机内参
      cv::Mat camera_matrix = (cv::Mat_<double>(3, 3) << camera_info_.k[0], camera_info_.k[1], camera_info_.k[2], camera_info_.k[3], camera_info_.k[4], camera_info_.k[5], camera_info_.k[6], camera_info_.k[7], camera_info_.k[8]);
      cv::Mat dist_coeffs = (cv::Mat_<double>(5, 1) << camera_info_.d[0], camera_info_.d[1], camera_info_.d[2], camera_info_.d[3], camera_info_.d[4]);
      // 计算 PNP
      cv::Mat rvec, tvec, inliers;
      cv::solvePnP(object_points, image_points, camera_matrix, dist_coeffs, rvec, tvec);
      // EPNP
      // cv::solvePnP(object_points, image_points, camera_matrix, dist_coeffs, rvec, tvec, false, cv::SOLVEPNP_P3P);
      // RANSAC
      // cv::solvePnPRansac(object_points, image_points, camera_matrix, dist_coeffs, rvec, tvec, false, 100, 8.0, 0.99, inliers, cv::SOLVEPNP_ITERATIVE);
      // std::cout << "rvec: " << rvec << std::endl;
      // std::cout << "tvec: " << tvec << std::endl;
      // 发布 pnp 转换后的结果
      pub_pnp_pose(rvec, tvec);
      // RCLCPP_INFO(this->get_logger(), "Hough circle detected 5 circles. . .");
      cv::imshow("ROI", roi);
      cv::waitKey(1);
    }
  }


  // 订阅者
  std::shared_ptr<message_filters::Subscriber<sensor_msgs::msg::Image>> sub_image_;
  std::shared_ptr<message_filters::Subscriber<vision_msgs::msg::BoundingBox2DArray>> sub_bbox_;
  using SyncPolicy = message_filters::sync_policies::ApproximateTime<sensor_msgs::msg::Image, vision_msgs::msg::BoundingBox2DArray>;
  std::shared_ptr<message_filters::Synchronizer<SyncPolicy>> sync_;
  rclcpp::TimerBase::SharedPtr timer_;
  rclcpp::TimerBase::SharedPtr tf_timer_;
  rclcpp::Client<data_interface::srv::PoseEst3d2dSrv>::SharedPtr client_;

  // 发布者
  rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pub_charge_pose_;

  // 相机参数
  CameraInfo camera_info_;

  std::shared_ptr<tf2_ros::Buffer> tf_buffer_;
  std::shared_ptr<tf2_ros::TransformListener> tf_listener_;
  cv::Mat T_base_end = cv::Mat::eye(4, 4, CV_64F);

  // 服务请求状态管理
  bool service_request_pending_ = false;  // 是否有请求正在处理
  bool service_request_completed_ = false;  // 是否已经完成过请求
  std::chrono::steady_clock::time_point last_request_time_;  // 上次请求时间
  static constexpr std::chrono::seconds REQUEST_COOLDOWN{2};  // 请求冷却时间
};

int main(int argc, char * argv[]) {
  rclcpp::init(argc, argv);
  auto node = std::make_shared<DetectedPostProcessing>();
  rclcpp::spin(node);
  rclcpp::shutdown();
  return 0;
}